Using pre-set license
Built from '6000.0/staging' branch; Version is '6000.0.47f1 (2ad1ed33fd3b) revision 2806253'; Using compiler version '193933523'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit CoreSingleLanguage' Language: 'fr' Physical Memory: 16051 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker39
-projectPath
C:/Users/<USER>/ONU
-logFile
Logs/AssetImportWorker39.log
-srvPort
56204
-job-worker-count
7
-background-job-worker-count
8
-gc-helper-count
1
Successfully changed project path to: C:/Users/<USER>/ONU
C:/Users/<USER>/ONU
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [48580]  Target information:

Player connection [48580]  * "[IP] *********** [Port] 0 [Flags] 2 [Guid] 2536518294 [EditorId] 2536518294 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-34E5P9N) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [48580] Host joined multi-casting on [***********:54997]...
Player connection [48580] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 7
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 3.82 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.0.47f1 (2ad1ed33fd3b)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path C:/Users/<USER>/ONU/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce GTX 1650 (ID=0x1f9d)
    Vendor:   NVIDIA
    VRAM:     3937 MB
    Driver:   32.0.15.7602
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56828
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Registered in 0.002378 seconds.
- Loaded All Assemblies, in  0.357 seconds
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 229 ms
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.510 seconds
Domain Reload Profiling: 865ms
	BeginReloadAssembly (148ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (30ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (39ms)
	LoadAllAssembliesAndSetupDomain (129ms)
		LoadAssemblies (145ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (126ms)
			TypeCache.Refresh (124ms)
				TypeCache.ScanAssembly (113ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (0ms)
	FinalizeReload (510ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (474ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (299ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (38ms)
			ProcessInitializeOnLoadAttributes (89ms)
			ProcessInitializeOnLoadMethodAttributes (45ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in 34.339 seconds
Refreshing native plugins compatible for Editor in 8.22 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation. ---> System.NullReferenceException: Object reference not set to an instance of an object
  at Unity.Multiplayer.Playmode.Workflow.Editor.ProjectDataStore..ctor (System.String directory) [0x0006e] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Configuration\ProjectDataStore.cs:55 
  at Unity.Multiplayer.Playmode.Workflow.Editor.ProjectDataStore.GetMain () [0x00000] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Configuration\ProjectDataStore.cs:36 
  at Unity.Multiplayer.Playmode.Workflow.Editor.WorkflowMainEditorContext..ctor (Unity.Multiplayer.Playmode.VirtualProjects.Editor.MainEditorContext mainEditorContext) [0x0002c] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Contexts\WorkflowMainEditorContext.cs:15 
  at Unity.Multiplayer.Playmode.Workflow.Editor.VirtualProjectWorkflow.InitializeMPPMContexts () [0x00018] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Workflow\VirtualProjectWorkflow.cs:198 
  at Unity.Multiplayer.Playmode.Workflow.Editor.VirtualProjectWorkflow+<>c.<.cctor>b__22_0 () [0x00036] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Workflow\VirtualProjectWorkflow.cs:85 
  at Unity.Multiplayer.Playmode.VirtualProjects.Editor.EditorContexts.SendReadyEvent () [0x00038] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\VirtualProjects\Editor\Contexts\EditorContexts.cs:56 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <********************************>:0 
   --- End of inner exception stack trace ---
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x00083] in <********************************>:0 
  at System.Reflection.MethodBase.Invoke (System.Object obj, System.Object[] parameters) [0x00000] in <********************************>:0 
  at UnityEditor.EditorAssemblies.ProcessInitializeOnLoadMethodAttributes () [0x00056] in <f8e1f14ef858402482f58cef2e987b5b>:0 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

Mirror | mirror-networking.com | discord.gg/N9QVxbM
Mono: successfully reloaded assembly
Package Manager log level set to [2]
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  1.645 seconds
Domain Reload Profiling: 35983ms
	BeginReloadAssembly (124ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (22ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (31ms)
	LoadAllAssembliesAndSetupDomain (34145ms)
		LoadAssemblies (478ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (33739ms)
			TypeCache.Refresh (214ms)
				TypeCache.ScanAssembly (189ms)
			BuildScriptInfoCaches (33387ms)
			ResolveRequiredComponents (111ms)
	FinalizeReload (1646ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1171ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (18ms)
			SetLoadedEditorAssemblies (8ms)
			BeforeProcessingInitializeOnLoad (197ms)
			ProcessInitializeOnLoadAttributes (584ms)
			ProcessInitializeOnLoadMethodAttributes (360ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.02 seconds
Refreshing native plugins compatible for Editor in 4.57 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 219 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7348 unused Assets / (7.0 MB). Loaded Objects now: 8010.
Memory consumption went from 184.9 MB to 177.8 MB.
Total: 15.315500 ms (FindLiveObjects: 0.846400 ms CreateObjectMapping: 1.114100 ms MarkObjects: 8.346900 ms  DeleteObjects: 5.004000 ms)

========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 2.07 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 33 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7327 unused Assets / (7.0 MB). Loaded Objects now: 8011.
Memory consumption went from 152.7 MB to 145.7 MB.
Total: 37.892600 ms (FindLiveObjects: 1.214300 ms CreateObjectMapping: 0.625600 ms MarkObjects: 32.470100 ms  DeleteObjects: 3.581400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in 12.404 seconds
Refreshing native plugins compatible for Editor in 1.61 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation. ---> System.NullReferenceException: Object reference not set to an instance of an object
  at Unity.Multiplayer.Playmode.Workflow.Editor.ProjectDataStore..ctor (System.String directory) [0x0006e] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Configuration\ProjectDataStore.cs:55 
  at Unity.Multiplayer.Playmode.Workflow.Editor.ProjectDataStore.GetMain () [0x00000] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Configuration\ProjectDataStore.cs:36 
  at Unity.Multiplayer.Playmode.Workflow.Editor.WorkflowMainEditorContext..ctor (Unity.Multiplayer.Playmode.VirtualProjects.Editor.MainEditorContext mainEditorContext) [0x0002c] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Contexts\WorkflowMainEditorContext.cs:15 
  at Unity.Multiplayer.Playmode.Workflow.Editor.VirtualProjectWorkflow.InitializeMPPMContexts () [0x00018] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Workflow\VirtualProjectWorkflow.cs:198 
  at Unity.Multiplayer.Playmode.Workflow.Editor.VirtualProjectWorkflow+<>c.<.cctor>b__22_0 () [0x00036] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Workflow\VirtualProjectWorkflow.cs:85 
  at Unity.Multiplayer.Playmode.VirtualProjects.Editor.EditorContexts.SendReadyEvent () [0x00038] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\VirtualProjects\Editor\Contexts\EditorContexts.cs:56 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <********************************>:0 
   --- End of inner exception stack trace ---
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x00083] in <********************************>:0 
  at System.Reflection.MethodBase.Invoke (System.Object obj, System.Object[] parameters) [0x00000] in <********************************>:0 
  at UnityEditor.EditorAssemblies.ProcessInitializeOnLoadMethodAttributes () [0x00056] in <f8e1f14ef858402482f58cef2e987b5b>:0 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  0.892 seconds
Domain Reload Profiling: 13297ms
	BeginReloadAssembly (911ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (14ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (90ms)
	RebuildCommonClasses (329ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (26ms)
	LoadAllAssembliesAndSetupDomain (11128ms)
		LoadAssemblies (10586ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (1294ms)
			TypeCache.Refresh (14ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (1262ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (892ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (583ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (10ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (95ms)
			ProcessInitializeOnLoadAttributes (316ms)
			ProcessInitializeOnLoadMethodAttributes (151ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 2.98 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 43 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7347 unused Assets / (7.1 MB). Loaded Objects now: 8026.
Memory consumption went from 167.3 MB to 160.2 MB.
Total: 12.378400 ms (FindLiveObjects: 0.840700 ms CreateObjectMapping: 0.894400 ms MarkObjects: 6.392800 ms  DeleteObjects: 4.249300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.799 seconds
Refreshing native plugins compatible for Editor in 1.62 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation. ---> System.NullReferenceException: Object reference not set to an instance of an object
  at Unity.Multiplayer.Playmode.Workflow.Editor.ProjectDataStore..ctor (System.String directory) [0x0006e] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Configuration\ProjectDataStore.cs:55 
  at Unity.Multiplayer.Playmode.Workflow.Editor.ProjectDataStore.GetMain () [0x00000] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Configuration\ProjectDataStore.cs:36 
  at Unity.Multiplayer.Playmode.Workflow.Editor.WorkflowMainEditorContext..ctor (Unity.Multiplayer.Playmode.VirtualProjects.Editor.MainEditorContext mainEditorContext) [0x0002c] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Contexts\WorkflowMainEditorContext.cs:15 
  at Unity.Multiplayer.Playmode.Workflow.Editor.VirtualProjectWorkflow.InitializeMPPMContexts () [0x00018] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Workflow\VirtualProjectWorkflow.cs:198 
  at Unity.Multiplayer.Playmode.Workflow.Editor.VirtualProjectWorkflow+<>c.<.cctor>b__22_0 () [0x00036] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Workflow\VirtualProjectWorkflow.cs:85 
  at Unity.Multiplayer.Playmode.VirtualProjects.Editor.EditorContexts.SendReadyEvent () [0x00038] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\VirtualProjects\Editor\Contexts\EditorContexts.cs:56 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <********************************>:0 
   --- End of inner exception stack trace ---
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x00083] in <********************************>:0 
  at System.Reflection.MethodBase.Invoke (System.Object obj, System.Object[] parameters) [0x00000] in <********************************>:0 
  at UnityEditor.EditorAssemblies.ProcessInitializeOnLoadMethodAttributes () [0x00056] in <f8e1f14ef858402482f58cef2e987b5b>:0 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  0.757 seconds
Domain Reload Profiling: 1556ms
	BeginReloadAssembly (197ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (49ms)
	RebuildCommonClasses (31ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (23ms)
	LoadAllAssembliesAndSetupDomain (538ms)
		LoadAssemblies (449ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (193ms)
			TypeCache.Refresh (10ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (165ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (757ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (595ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (9ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (111ms)
			ProcessInitializeOnLoadAttributes (324ms)
			ProcessInitializeOnLoadMethodAttributes (141ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 4.67 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 33 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7347 unused Assets / (7.7 MB). Loaded Objects now: 8028.
Memory consumption went from 167.5 MB to 159.8 MB.
Total: 19.319000 ms (FindLiveObjects: 0.812900 ms CreateObjectMapping: 2.412400 ms MarkObjects: 8.844300 ms  DeleteObjects: 7.247300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.686 seconds
Refreshing native plugins compatible for Editor in 1.90 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation. ---> System.NullReferenceException: Object reference not set to an instance of an object
  at Unity.Multiplayer.Playmode.Workflow.Editor.ProjectDataStore..ctor (System.String directory) [0x0006e] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Configuration\ProjectDataStore.cs:55 
  at Unity.Multiplayer.Playmode.Workflow.Editor.ProjectDataStore.GetMain () [0x00000] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Configuration\ProjectDataStore.cs:36 
  at Unity.Multiplayer.Playmode.Workflow.Editor.WorkflowMainEditorContext..ctor (Unity.Multiplayer.Playmode.VirtualProjects.Editor.MainEditorContext mainEditorContext) [0x0002c] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Contexts\WorkflowMainEditorContext.cs:15 
  at Unity.Multiplayer.Playmode.Workflow.Editor.VirtualProjectWorkflow.InitializeMPPMContexts () [0x00018] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Workflow\VirtualProjectWorkflow.cs:198 
  at Unity.Multiplayer.Playmode.Workflow.Editor.VirtualProjectWorkflow+<>c.<.cctor>b__22_0 () [0x00036] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Workflow\VirtualProjectWorkflow.cs:85 
  at Unity.Multiplayer.Playmode.VirtualProjects.Editor.EditorContexts.SendReadyEvent () [0x00038] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\VirtualProjects\Editor\Contexts\EditorContexts.cs:56 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <********************************>:0 
   --- End of inner exception stack trace ---
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x00083] in <********************************>:0 
  at System.Reflection.MethodBase.Invoke (System.Object obj, System.Object[] parameters) [0x00000] in <********************************>:0 
  at UnityEditor.EditorAssemblies.ProcessInitializeOnLoadMethodAttributes () [0x00056] in <f8e1f14ef858402482f58cef2e987b5b>:0 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  0.708 seconds
Domain Reload Profiling: 1394ms
	BeginReloadAssembly (173ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (49ms)
	RebuildCommonClasses (30ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (24ms)
	LoadAllAssembliesAndSetupDomain (448ms)
		LoadAssemblies (330ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (196ms)
			TypeCache.Refresh (11ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (169ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (708ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (551ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (9ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (101ms)
			ProcessInitializeOnLoadAttributes (285ms)
			ProcessInitializeOnLoadMethodAttributes (147ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 2.87 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 33 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7347 unused Assets / (7.4 MB). Loaded Objects now: 8030.
Memory consumption went from 167.5 MB to 160.1 MB.
Total: 13.001600 ms (FindLiveObjects: 0.766700 ms CreateObjectMapping: 0.971800 ms MarkObjects: 6.808600 ms  DeleteObjects: 4.452700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.656 seconds
Refreshing native plugins compatible for Editor in 1.70 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation. ---> System.NullReferenceException: Object reference not set to an instance of an object
  at Unity.Multiplayer.Playmode.Workflow.Editor.ProjectDataStore..ctor (System.String directory) [0x0006e] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Configuration\ProjectDataStore.cs:55 
  at Unity.Multiplayer.Playmode.Workflow.Editor.ProjectDataStore.GetMain () [0x00000] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Configuration\ProjectDataStore.cs:36 
  at Unity.Multiplayer.Playmode.Workflow.Editor.WorkflowMainEditorContext..ctor (Unity.Multiplayer.Playmode.VirtualProjects.Editor.MainEditorContext mainEditorContext) [0x0002c] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Contexts\WorkflowMainEditorContext.cs:15 
  at Unity.Multiplayer.Playmode.Workflow.Editor.VirtualProjectWorkflow.InitializeMPPMContexts () [0x00018] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Workflow\VirtualProjectWorkflow.cs:198 
  at Unity.Multiplayer.Playmode.Workflow.Editor.VirtualProjectWorkflow+<>c.<.cctor>b__22_0 () [0x00036] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Workflow\VirtualProjectWorkflow.cs:85 
  at Unity.Multiplayer.Playmode.VirtualProjects.Editor.EditorContexts.SendReadyEvent () [0x00038] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\VirtualProjects\Editor\Contexts\EditorContexts.cs:56 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <********************************>:0 
   --- End of inner exception stack trace ---
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x00083] in <********************************>:0 
  at System.Reflection.MethodBase.Invoke (System.Object obj, System.Object[] parameters) [0x00000] in <********************************>:0 
  at UnityEditor.EditorAssemblies.ProcessInitializeOnLoadMethodAttributes () [0x00056] in <f8e1f14ef858402482f58cef2e987b5b>:0 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  0.752 seconds
Domain Reload Profiling: 1409ms
	BeginReloadAssembly (161ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (41ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (25ms)
	LoadAllAssembliesAndSetupDomain (432ms)
		LoadAssemblies (319ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (191ms)
			TypeCache.Refresh (10ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (163ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (753ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (586ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (9ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (115ms)
			ProcessInitializeOnLoadAttributes (299ms)
			ProcessInitializeOnLoadMethodAttributes (153ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 3.27 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 33 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7348 unused Assets / (7.6 MB). Loaded Objects now: 8033.
Memory consumption went from 167.5 MB to 159.9 MB.
Total: 13.551600 ms (FindLiveObjects: 1.008800 ms CreateObjectMapping: 1.084400 ms MarkObjects: 6.465600 ms  DeleteObjects: 4.991100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 1650096.952496 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_SUV_02C.fbx
  artifactKey: Guid(f751fec13c10d5f419298d33f272ba9c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_SUV_02C.fbx using Guid(f751fec13c10d5f419298d33f272ba9c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '404026b13bd7633fae941d06795ebb4d') in 0.9058823 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 77

========================================================================
Received Import Request.
  Time since last request: 0.013651 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Wall_Frame_02B.fbx
  artifactKey: Guid(be813bceba66c774e9a5b2ed9f29ab68) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Wall_Frame_02B.fbx using Guid(be813bceba66c774e9a5b2ed9f29ab68) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '050e6fa96866787dada7fc17cecba934') in 0.035927 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.021472 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Wall_Frame_03B.fbx
  artifactKey: Guid(b72f8824d658c164aa385ca6789bb98b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Wall_Frame_03B.fbx using Guid(b72f8824d658c164aa385ca6789bb98b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '197717d5d8674205d63aeeb4d8a7f8c9') in 0.0270018 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Wall_Frame_03A.fbx
  artifactKey: Guid(52dfc0a39a9f7cf4a9d906ff68c5bd0d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Wall_Frame_03A.fbx using Guid(52dfc0a39a9f7cf4a9d906ff68c5bd0d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c9701ce48e30344174e89ab8cd013444') in 0.0295501 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.099039 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Wall_Clock_01B.fbx
  artifactKey: Guid(7d049d2f0f5e7664c804eb2bffbf2b5a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Wall_Clock_01B.fbx using Guid(7d049d2f0f5e7664c804eb2bffbf2b5a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '7d5fe13f4b1f0f070385960b45ab4bdc') in 0.0351069 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 21

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/Toon Suburban Pack/Models/Colliders/TSP_Umbrella_01A_MeshCollider.fbx
  artifactKey: Guid(dda2482fdb9f6904f820848d39c06855) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/Colliders/TSP_Umbrella_01A_MeshCollider.fbx using Guid(dda2482fdb9f6904f820848d39c06855) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '7a318ed0b1af45e11711bb8eab83893b') in 0.0257211 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Wall_Frame_01B.fbx
  artifactKey: Guid(74acfd840653d6c4a8dfe4853d159344) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Wall_Frame_01B.fbx using Guid(74acfd840653d6c4a8dfe4853d159344) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '527776e4b3156e200b7905657c319ef2') in 0.0282167 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_TV_Stand_02A.fbx
  artifactKey: Guid(edbd0ea807447ee4e94d07c18f05de44) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_TV_Stand_02A.fbx using Guid(edbd0ea807447ee4e94d07c18f05de44) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'fd8cfc40f0ef99488edc6bbfd363cf20') in 0.027692 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_TV_01A.fbx
  artifactKey: Guid(69f0f4e73a6f3164480ecf2c4d0ded10) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_TV_01A.fbx using Guid(69f0f4e73a6f3164480ecf2c4d0ded10) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '15f587f0a11ceda06cd178f185d072a9') in 0.0735142 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Tree_04A.fbx
  artifactKey: Guid(2215d9c865a624a47b3f42b311c7b897) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Tree_04A.fbx using Guid(2215d9c865a624a47b3f42b311c7b897) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '37a61fa598b65909992c8d5054566e98') in 0.1303935 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 19

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Trash_Can_01A.fbx
  artifactKey: Guid(5e4d254ca86961240ad755eca2ca0e0d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Trash_Can_01A.fbx using Guid(5e4d254ca86961240ad755eca2ca0e0d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b690d43e2b04bfd83208e6572837accb') in 0.0361044 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Trash_Container_01A.fbx
  artifactKey: Guid(04408cd2f384a4d4f9d496a43e5fb543) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Trash_Container_01A.fbx using Guid(04408cd2f384a4d4f9d496a43e5fb543) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '7cfa05ef5e26f72e9236727276c44c5d') in 0.0343959 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Trash_Container_01B.fbx
  artifactKey: Guid(8451352ef5309ee4da7655ce3f27bc00) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Trash_Container_01B.fbx using Guid(8451352ef5309ee4da7655ce3f27bc00) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '72c3ee558386d852c55bb20f910aacb8') in 0.0375789 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Toon Suburban Pack/Models/Colliders/TSP_SUV_02A_Trunk_MeshCollider.fbx
  artifactKey: Guid(101f5766e63b00445a9b60cd980f97d9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/Colliders/TSP_SUV_02A_Trunk_MeshCollider.fbx using Guid(101f5766e63b00445a9b60cd980f97d9) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '32511fd2c4faa47677fa4c4237741a40') in 0.021967 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Tree_01A.fbx
  artifactKey: Guid(4c4086ae0d1858d40b6480a3b304aab5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Tree_01A.fbx using Guid(4c4086ae0d1858d40b6480a3b304aab5) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'fcfd972c5b58ce6ca016ac6a1be16f9e') in 0.0331493 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 19

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Tree_02C.fbx
  artifactKey: Guid(446fba96486ee1f4e946b4b3dc88c4fa) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Tree_02C.fbx using Guid(446fba96486ee1f4e946b4b3dc88c4fa) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '299fa9a2b7b305c4f3844e176cbc3559') in 0.0417817 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 19

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_TV_Stand_02C.fbx
  artifactKey: Guid(ae26ed45aa30fb44ab8717d2321c14ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_TV_Stand_02C.fbx using Guid(ae26ed45aa30fb44ab8717d2321c14ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '69210fec8f18d8a9a69137f31bb77644') in 0.0291394 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Tree_02A.fbx
  artifactKey: Guid(7e85c59f892883441b0b6c845eebaa4c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Tree_02A.fbx using Guid(7e85c59f892883441b0b6c845eebaa4c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f7f9c9fb7f5f858c37d5785c6535b3a9') in 0.0426296 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 19

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_TV_Wall_01A.fbx
  artifactKey: Guid(02b730b78978cda40b0be2b7d5c694f6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_TV_Wall_01A.fbx using Guid(02b730b78978cda40b0be2b7d5c694f6) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '54269d1eb53a2a6a81edde9f9a31b030') in 0.0277332 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Table_01A.fbx
  artifactKey: Guid(a8b3fbadf4314d74c8122463c86cffe6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Table_01A.fbx using Guid(a8b3fbadf4314d74c8122463c86cffe6) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '890ed1558496830849b7c9df3cc745f9') in 0.0303464 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Table_01B.fbx
  artifactKey: Guid(aca877e85acf582489e3ccb3d9258f0e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Table_01B.fbx using Guid(aca877e85acf582489e3ccb3d9258f0e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '8c985a4145c0cfa205679a51a0af28d2') in 0.0241224 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Tree_02B.fbx
  artifactKey: Guid(61dc61b07104fef4589b8360d8a5219f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Tree_02B.fbx using Guid(61dc61b07104fef4589b8360d8a5219f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'aabe090de18c22ebe9b7df32b429606f') in 0.0379597 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 19

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_TV_Stand_02B.fbx
  artifactKey: Guid(ab30082b3ff1fb641b186bcaacc48528) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_TV_Stand_02B.fbx using Guid(ab30082b3ff1fb641b186bcaacc48528) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '804154f6fb66e839294cd759d9fc8752') in 0.0280767 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 7.700225 seconds.
  path: Assets/Toon Suburban Pack/Models/Colliders/TSP_Metal_Table_01A_MeshCollider.fbx
  artifactKey: Guid(dd7fb4c4e6b5a394a85666223aa6b77b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/Colliders/TSP_Metal_Table_01A_MeshCollider.fbx using Guid(dd7fb4c4e6b5a394a85666223aa6b77b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '361e703d1378d663b90926df304939b0') in 0.9336583 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/Toon Suburban Pack/Models/Colliders/TSP_Minivan_01A_Door_F.R_MeshCollider.fbx
  artifactKey: Guid(648ddc58dba918e4cbda98385a2a2b3d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/Colliders/TSP_Minivan_01A_Door_F.R_MeshCollider.fbx using Guid(648ddc58dba918e4cbda98385a2a2b3d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f5dcdb2c2a6ac36c36c389dc3b5928ba') in 0.0583597 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.780983 seconds.
  path: Assets/Toon Suburban Pack/Models/Colliders/TSP_Minivan_01A_Door_R.R_MeshCollider.fbx
  artifactKey: Guid(0d822452faba6824194a5347e2141a9e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/Colliders/TSP_Minivan_01A_Door_R.R_MeshCollider.fbx using Guid(0d822452faba6824194a5347e2141a9e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '5718d63821c0be641667135b93b89407') in 0.0278334 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 8.560952 seconds.
  path: Assets/Toon Suburban Pack/Models/Colliders/TSP_Lounge_Chair_01A_MeshCollider.fbx
  artifactKey: Guid(e521cce84843de049b93cd9d4c51fa98) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/Colliders/TSP_Lounge_Chair_01A_MeshCollider.fbx using Guid(e521cce84843de049b93cd9d4c51fa98) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '188be10316c62e749887442bd1ae93ff') in 0.027585 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 13.111294 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Kitchen_Cabinet_Top_Corner_01B.fbx
  artifactKey: Guid(a482d3e884fc8a343ac69a81f7c741eb) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Kitchen_Cabinet_Top_Corner_01B.fbx using Guid(a482d3e884fc8a343ac69a81f7c741eb) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '3377e71d27a78846d5207355a692d4ba') in 0.0695895 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 1.471936 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Kitchen_Cabinet_Top_04A.fbx
  artifactKey: Guid(9a609cf2b59207541b2d6c862ed38792) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Kitchen_Cabinet_Top_04A.fbx using Guid(9a609cf2b59207541b2d6c862ed38792) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f2f6542ed025524becaee6aca28b0b57') in 0.0272838 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.395724 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Kitchen_Cabinet_Top_03A.fbx
  artifactKey: Guid(8016e6f732f20374cb129e23ab2baf77) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Kitchen_Cabinet_Top_03A.fbx using Guid(8016e6f732f20374cb129e23ab2baf77) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '67879046be411e6c339933a01cb78eaa') in 0.0388247 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 21

========================================================================
Received Import Request.
  Time since last request: 2.175523 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Kitchen_Cabinet_Stove_01A.fbx
  artifactKey: Guid(17c2d8af56fb3f44c9355662e4ccb493) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Kitchen_Cabinet_Stove_01A.fbx using Guid(17c2d8af56fb3f44c9355662e4ccb493) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f6c942397d6aba6a6a992f324ae3034f') in 0.0411717 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.002187 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Kitchen_Cabinet_End_01B.fbx
  artifactKey: Guid(ac12c06d9e19df94a907ae36f16ec992) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Kitchen_Cabinet_End_01B.fbx using Guid(ac12c06d9e19df94a907ae36f16ec992) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '14459ced1ca9cff145b530d01c7626ae') in 0.0428392 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 56.566561 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Kitchen_Cabinet_04B.fbx
  artifactKey: Guid(4d37ccd0b4383474f8ccd1873e723cd9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Kitchen_Cabinet_04B.fbx using Guid(4d37ccd0b4383474f8ccd1873e723cd9) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '90239e900a9bac2e17afe33525e236d3') in 0.0302294 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.096229 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Kitchen_Cabinet_03B.fbx
  artifactKey: Guid(2248b97875688cf4ca9859323e6f1ef7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Kitchen_Cabinet_03B.fbx using Guid(2248b97875688cf4ca9859323e6f1ef7) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd384a53cb8699fdda885f28b39480912') in 0.0527003 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 26

========================================================================
Received Import Request.
  Time since last request: 0.262547 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Kitchen_Cabinet_02B.fbx
  artifactKey: Guid(a4424f4139ab2a049900b5eb87f8ffc8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Kitchen_Cabinet_02B.fbx using Guid(a4424f4139ab2a049900b5eb87f8ffc8) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c09918905aa6a2dfc9213394b1945041') in 0.0330757 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.114435 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Kitchen_Cabinet_01B.fbx
  artifactKey: Guid(8595fc879d9288b4d9b75ab941decc27) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Kitchen_Cabinet_01B.fbx using Guid(8595fc879d9288b4d9b75ab941decc27) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '118c793e0d383da135264f503fcc1a1a') in 0.0395566 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 21

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Jar_01B.fbx
  artifactKey: Guid(93b5b0be98ac62e459c4a6f29abcde6e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Jar_01B.fbx using Guid(93b5b0be98ac62e459c4a6f29abcde6e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'dd9ef15773c8f67f67add0acb9a7e2d5') in 0.0259564 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.049252 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Interior_Wall_Short_06A.fbx
  artifactKey: Guid(c61b56b0b87454b4c9da516da61ed6b8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Interior_Wall_Short_06A.fbx using Guid(c61b56b0b87454b4c9da516da61ed6b8) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c9d8e2c0ce280d025b46a33b870c0b8c') in 0.0579451 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.073578 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Interior_Wall_Short_02B.fbx
  artifactKey: Guid(f11aee6c0b15c5243b0ac862d0a68341) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Interior_Wall_Short_02B.fbx using Guid(f11aee6c0b15c5243b0ac862d0a68341) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '32b91ee7d0b875468b2cfa698b3892f4') in 0.0238238 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Interior_Wall_Short_04A.fbx
  artifactKey: Guid(6f6a015878a65d548a140d9ba4fd9230) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Interior_Wall_Short_04A.fbx using Guid(6f6a015878a65d548a140d9ba4fd9230) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'afced585753222255aa3995c0a436359') in 0.0368145 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 18

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Interior_Wall_Regular_05B.fbx
  artifactKey: Guid(70ca38fd97883dc4d803c1f3bbef72f0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Interior_Wall_Regular_05B.fbx using Guid(70ca38fd97883dc4d803c1f3bbef72f0) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '5535b51d690817447a2302549dec213b') in 0.0257591 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Interior_Wall_Regular_06B.fbx
  artifactKey: Guid(a055aa9d421384d479e1aa3742853f82) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Interior_Wall_Regular_06B.fbx using Guid(a055aa9d421384d479e1aa3742853f82) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '6e81641e9f92d8af9593b2baca1373a0') in 0.0274559 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Interior_Wall_Regular_05A.fbx
  artifactKey: Guid(9cfbea2b33e6f1140a0b3b358ec428e9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Interior_Wall_Regular_05A.fbx using Guid(9cfbea2b33e6f1140a0b3b358ec428e9) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '3cb7d50008a50598c91e938ae0a93334') in 0.0249508 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.043621 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Interior_Wall_Regular_02B.fbx
  artifactKey: Guid(863603e06c6dd5c4f975e842b5fe6eae) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Interior_Wall_Regular_02B.fbx using Guid(863603e06c6dd5c4f975e842b5fe6eae) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '23a35a1e718bdaf92b6269a277d083ef') in 0.0248697 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.020559 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Hydrant_01A.fbx
  artifactKey: Guid(a8056ca55f30d594f86b0ca12c9fecb5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Hydrant_01A.fbx using Guid(a8056ca55f30d594f86b0ca12c9fecb5) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd7b9906b0bc0caf15f844ec8cc707b73') in 0.0267427 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.007425 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Wall_Special_04A.fbx
  artifactKey: Guid(d43334f6e99ed834f9dfbccc5013061a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Wall_Special_04A.fbx using Guid(d43334f6e99ed834f9dfbccc5013061a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c32ce33c5ba4d11653de5dd70314a00d') in 0.058553 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 33

========================================================================
Received Import Request.
  Time since last request: 0.173665 seconds.
  path: Assets/Toon Suburban Pack/Models/Colliders/TSP_House_Wall_Special_03A_MeshCollider.fbx
  artifactKey: Guid(0b014a111d63ca946ba5bfd252cb14ac) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/Colliders/TSP_House_Wall_Special_03A_MeshCollider.fbx using Guid(0b014a111d63ca946ba5bfd252cb14ac) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0878ba1a42eca3e97d9e2fda7375d22e') in 0.0272073 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.057981 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Wall_Special_01B.fbx
  artifactKey: Guid(3738c1ca7dca3784f8fbc7ac6e2c5459) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Wall_Special_01B.fbx using Guid(3738c1ca7dca3784f8fbc7ac6e2c5459) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '5124d325bc970cb5d0c3b5e38d171ead') in 0.0435265 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 18

========================================================================
Received Import Request.
  Time since last request: 0.276022 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Wall_Short_15B.fbx
  artifactKey: Guid(65b05be601ba46b469749d09564998cf) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Wall_Short_15B.fbx using Guid(65b05be601ba46b469749d09564998cf) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c9204256ba2db437f8c0f4bde7fd42de') in 0.0389757 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Wall_Short_14A.fbx
  artifactKey: Guid(47028b6fb6eaf9d48a62d2438095583c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Wall_Short_14A.fbx using Guid(47028b6fb6eaf9d48a62d2438095583c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '320acb9d99abfdf9136e16a0864fb861') in 0.0273211 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Wall_Short_11B.fbx
  artifactKey: Guid(50beeb50839481d4aba76e0c83f6fd10) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Wall_Short_11B.fbx using Guid(50beeb50839481d4aba76e0c83f6fd10) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e2592a2b55d77e00e5856acbada648eb') in 0.0629189 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.277779 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Wall_Short_10A.fbx
  artifactKey: Guid(08a449047fa04eb48a47b2b896b958c1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Wall_Short_10A.fbx using Guid(08a449047fa04eb48a47b2b896b958c1) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'cb2844729a0621e4855acce424c81f5e') in 0.0436083 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 23

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Wall_Short_09A.fbx
  artifactKey: Guid(ddb1dce64ccfc8b448c769477bd0e989) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Wall_Short_09A.fbx using Guid(ddb1dce64ccfc8b448c769477bd0e989) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f4e54b771b0d52d62c5c57df87ff8dca') in 0.0409798 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 18

========================================================================
Received Import Request.
  Time since last request: 0.000721 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Wall_Short_07A.fbx
  artifactKey: Guid(0857f36003eb7c541bfdcdcedb49685c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Wall_Short_07A.fbx using Guid(0857f36003eb7c541bfdcdcedb49685c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '90e2efc5e2213e3fac16b232f32d4f5b') in 0.0240284 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.164710 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Wall_Short_05A.fbx
  artifactKey: Guid(468007b02707bf84293aec53d69d899d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Wall_Short_05A.fbx using Guid(468007b02707bf84293aec53d69d899d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'eb0dee6ede772bffe723bad7505b3d8c') in 0.027218 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/Toon Suburban Pack/Models/Colliders/TSP_House_Wall_Short_06A_MeshCollider.fbx
  artifactKey: Guid(70f7cc94603fd3149aa14b775569e3e3) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/Colliders/TSP_House_Wall_Short_06A_MeshCollider.fbx using Guid(70f7cc94603fd3149aa14b775569e3e3) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '4131853ae4a7316bf3152c99bac32dc6') in 0.0234957 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Wall_Short_04A.fbx
  artifactKey: Guid(a894ba6bb6d3a3f43887ac193219524d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Wall_Short_04A.fbx using Guid(a894ba6bb6d3a3f43887ac193219524d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '5b45faa4d9b8bd6d7f046d7620f648da') in 0.0647673 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 23

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Wall_Short_02A.fbx
  artifactKey: Guid(81bc59456f5c24247aebb7e04ce3b0f6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Wall_Short_02A.fbx using Guid(81bc59456f5c24247aebb7e04ce3b0f6) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '2c8fd3399f9e5a5eec100bf44ea315a8') in 0.0475971 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 18

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Wall_Short_01A.fbx
  artifactKey: Guid(3bc1bb88a747b4541ac5023e989c7af5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Wall_Short_01A.fbx using Guid(3bc1bb88a747b4541ac5023e989c7af5) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '85698496a3c6dbc16ef7815b94202dc5') in 0.0409387 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 18

========================================================================
Received Import Request.
  Time since last request: 0.008423 seconds.
  path: Assets/Toon Suburban Pack/Models/Colliders/TSP_House_Wall_Roof_03A_MeshCollider.fbx
  artifactKey: Guid(c92d362092f621544bbd8b7b9fd6dff5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/Colliders/TSP_House_Wall_Roof_03A_MeshCollider.fbx using Guid(c92d362092f621544bbd8b7b9fd6dff5) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '3a7e0b1ca7616584d4eea280f4f9243a') in 0.0220081 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Wall_Roof_04A_End.fbx
  artifactKey: Guid(6461885cabf220d4bab971556c327636) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Wall_Roof_04A_End.fbx using Guid(6461885cabf220d4bab971556c327636) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '4b960bcef96994ab81151d8886400e17') in 0.0281832 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Wall_Roof_05B.fbx
  artifactKey: Guid(b6306c5425ef98049b1c1046e0e2385c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Wall_Roof_05B.fbx using Guid(b6306c5425ef98049b1c1046e0e2385c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd417b2dbb01f53500c29befcf344afe2') in 0.0326345 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.011567 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Wall_Roof_02A_End.fbx
  artifactKey: Guid(6ba2d0ce1a84ec64db2a40d30cc56019) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Wall_Roof_02A_End.fbx using Guid(6ba2d0ce1a84ec64db2a40d30cc56019) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '74db6a78ce3b9c8c492f4e8d5f1c5955') in 0.0243764 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Wall_Roof_03A.fbx
  artifactKey: Guid(a554a50948c88c0419222b94686ba757) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Wall_Roof_03A.fbx using Guid(a554a50948c88c0419222b94686ba757) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '8c2f3f4183c213285e95a3cd2218246a') in 0.0323354 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Toon Suburban Pack/Models/Colliders/TSP_House_Wall_Roof_01A_MeshCollider.fbx
  artifactKey: Guid(a7264f31e7668f24a83d338d1420fa2c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/Colliders/TSP_House_Wall_Roof_01A_MeshCollider.fbx using Guid(a7264f31e7668f24a83d338d1420fa2c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '7482a36d111e87e9b3065fe097798efc') in 0.0270221 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.186002 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Wall_Regular_14A.fbx
  artifactKey: Guid(ac5aeb8aa103d9e46b27bc46244f2af1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Wall_Regular_14A.fbx using Guid(ac5aeb8aa103d9e46b27bc46244f2af1) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '65f9d8a208e1253337f759eea7839c7d') in 0.0279614 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Wall_Regular_11B.fbx
  artifactKey: Guid(1db97565b15052b46851b456dd3876dd) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Wall_Regular_11B.fbx using Guid(1db97565b15052b46851b456dd3876dd) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '8030ab717c0a4dafda483ea3d4c0cd46') in 0.0595026 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.021543 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Wall_Regular_10A.fbx
  artifactKey: Guid(55edf0b5f7030d94b87299acfe7de347) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Wall_Regular_10A.fbx using Guid(55edf0b5f7030d94b87299acfe7de347) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '80fd76b655ff8b7fd2498881a8e8178a') in 0.0598961 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.070246 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Wall_Regular_09A.fbx
  artifactKey: Guid(fc27e81d56c279e4c8f568dcbebd00b8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Wall_Regular_09A.fbx using Guid(fc27e81d56c279e4c8f568dcbebd00b8) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '6d0bf1e060ebe0ff4e242e31c72eea62') in 0.0566197 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.001474 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Wall_Regular_07B.fbx
  artifactKey: Guid(75ad5ae35f88e484980676a71c554eeb) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Wall_Regular_07B.fbx using Guid(75ad5ae35f88e484980676a71c554eeb) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c4f42842bb4cc1bfee2b8fcde47cf177') in 0.0233917 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.016978 seconds.
  path: Assets/Toon Suburban Pack/Models/Colliders/TSP_House_Wall_Regular_06A_MeshCollider.fbx
  artifactKey: Guid(8970903039263684d87f0b671030c155) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/Colliders/TSP_House_Wall_Regular_06A_MeshCollider.fbx using Guid(8970903039263684d87f0b671030c155) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '35f3383f1ddff20db213f23b1af5df37') in 0.0224248 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.017479 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Wall_Regular_04B.fbx
  artifactKey: Guid(a014ead1b6b09f840987d402260ad1a4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Wall_Regular_04B.fbx using Guid(a014ead1b6b09f840987d402260ad1a4) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'aee1b387cb834157f7cd5dcd1f506554') in 0.0411137 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 23

========================================================================
Received Import Request.
  Time since last request: 0.002856 seconds.
  path: Assets/Toon Suburban Pack/Models/Colliders/TSP_House_Wall_Regular_04A_MeshCollider.fbx
  artifactKey: Guid(6af05c77a62f91a40a08b57a16d70603) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/Colliders/TSP_House_Wall_Regular_04A_MeshCollider.fbx using Guid(6af05c77a62f91a40a08b57a16d70603) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '14bb7cc92fd1062832e331174dd24747') in 0.0238094 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.808542 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Wall_Regular_03A.fbx
  artifactKey: Guid(89ba4b015061a0a4388aebfe40c89995) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Wall_Regular_03A.fbx using Guid(89ba4b015061a0a4388aebfe40c89995) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'db91231cd24a1e145f2ae076b1c71358') in 0.0411853 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 23

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Wall_Rail_01A.fbx
  artifactKey: Guid(75d9eb5b1b790284cbcf30c6f381b02f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Wall_Rail_01A.fbx using Guid(75d9eb5b1b790284cbcf30c6f381b02f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '48227e585b903e93a3d92ef4e9374f05') in 0.0217119 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Wall_Regular_01A.fbx
  artifactKey: Guid(d1d5e7f8ca7d8d24a9105ad0bf63e22b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Wall_Regular_01A.fbx using Guid(d1d5e7f8ca7d8d24a9105ad0bf63e22b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '17fdcee8cb63f091713a2057837a6874') in 0.0399058 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 23

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Stairs_Corner_01A.R.fbx
  artifactKey: Guid(e3852869bfb462542a223149a18914cb) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Stairs_Corner_01A.R.fbx using Guid(e3852869bfb462542a223149a18914cb) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '093c245b7c9629d20dd76f5a48201e16') in 0.0223884 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Stairs_Corner_01C.L.fbx
  artifactKey: Guid(99c6362c410c0d94eae5128bc8170626) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Stairs_Corner_01C.L.fbx using Guid(99c6362c410c0d94eae5128bc8170626) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '5ef8c45b12ee9780a7b1338055cb8acb') in 0.0226715 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Stairs_Corner_01C.R.fbx
  artifactKey: Guid(74fdcd4dd46ddb34486047cdcfee606d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Stairs_Corner_01C.R.fbx using Guid(74fdcd4dd46ddb34486047cdcfee606d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e6bfa72539384cb32a2cce676cb3c1cf') in 0.0238549 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/Toon Suburban Pack/Models/Colliders/TSP_House_Stairs_Corner_Rails_01A.R_MeshCollider.fbx
  artifactKey: Guid(44f79027235f34748b54d64ef09b7725) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/Colliders/TSP_House_Stairs_Corner_Rails_01A.R_MeshCollider.fbx using Guid(44f79027235f34748b54d64ef09b7725) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '83d73294a81505ad2d9c421850395250') in 0.0252875 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Stairs_Rails_03A.fbx
  artifactKey: Guid(b438703aba27d6f43bb9d98ccad54e45) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Stairs_Rails_03A.fbx using Guid(b438703aba27d6f43bb9d98ccad54e45) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '55ea46f113497bad3c6496eece4fc706') in 0.0263744 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/Toon Suburban Pack/Models/Colliders/TSP_House_Wall_Entrance_01A_MeshCollider.fbx
  artifactKey: Guid(e4ee5debf6d64f045a1c06d53873c69d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/Colliders/TSP_House_Wall_Entrance_01A_MeshCollider.fbx using Guid(e4ee5debf6d64f045a1c06d53873c69d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f6fa272c96dd9b2eedcacc214ba4988c') in 0.0250426 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Wall_Brick_01B.fbx
  artifactKey: Guid(4b88996169d46554bb69990bc4695aa4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Wall_Brick_01B.fbx using Guid(4b88996169d46554bb69990bc4695aa4) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '56dcc84c2f84986fda4609c971bd1579') in 0.0241856 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Roof_04B_1.fbx
  artifactKey: Guid(d09366bed969d7642bd4418069078ea0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Roof_04B_1.fbx using Guid(d09366bed969d7642bd4418069078ea0) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd4420842f4ddb8c9d683e791d9c08900') in 0.0244146 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Roof_05A.fbx
  artifactKey: Guid(2b61c43a10bedbb41bbd988755acd8e0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Roof_05A.fbx using Guid(2b61c43a10bedbb41bbd988755acd8e0) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '626a6e3d7d7d5327fa394ed652370bcc') in 0.0239609 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Roof_04B_3.fbx
  artifactKey: Guid(c67d029797e054d4a86e05bb0fab9cfc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Roof_04B_3.fbx using Guid(c67d029797e054d4a86e05bb0fab9cfc) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0efd016f1ce2423a023650c46c8af7dc') in 0.0239442 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Roof_06B.fbx
  artifactKey: Guid(bdeaa1f2d31ff61489ff05cf1622f2dc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Roof_06B.fbx using Guid(bdeaa1f2d31ff61489ff05cf1622f2dc) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '82214608288521b4ded0c825e14deb3e') in 0.0254151 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Roof_06C_End.fbx
  artifactKey: Guid(f51607dac6196354faefc2cf6e784d57) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Roof_06C_End.fbx using Guid(f51607dac6196354faefc2cf6e784d57) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd22b24a9cf5f7abc9a5cb04e1481eb4a') in 0.025922 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Toon Suburban Pack/Models/Colliders/TSP_House_Stairs_01A_MeshCollider.fbx
  artifactKey: Guid(67eeaaf343333b3468e8441ca1e23181) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/Colliders/TSP_House_Stairs_01A_MeshCollider.fbx using Guid(67eeaaf343333b3468e8441ca1e23181) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e447ccd1d4fe0c31481b3ff9319c64e7') in 0.0240384 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Toon Suburban Pack/Models/Colliders/TSP_House_Roof_05A_MeshCollider.fbx
  artifactKey: Guid(b572a513426d6ad4688b89078698b8ae) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/Colliders/TSP_House_Roof_05A_MeshCollider.fbx using Guid(b572a513426d6ad4688b89078698b8ae) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '397f1e91d683b897738245ebc843995e') in 0.0299365 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Roof_06C.fbx
  artifactKey: Guid(3145bb315e465c545ad15c2edb91c1d4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Roof_06C.fbx using Guid(3145bb315e465c545ad15c2edb91c1d4) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0e2f7a41c743a1ab3b464944f53f0880') in 0.0345126 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/Toon Suburban Pack/Models/Colliders/TSP_House_Roof_06A_MeshCollider.fbx
  artifactKey: Guid(376097d60c995b84887064fe727f5b74) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/Colliders/TSP_House_Roof_06A_MeshCollider.fbx using Guid(376097d60c995b84887064fe727f5b74) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '8636f96d3a48c02419a9805c3331f699') in 0.0290503 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.374179 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Roof_03B_2.fbx
  artifactKey: Guid(2fd314f3bab4cff449d02839dd31d41d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Roof_03B_2.fbx using Guid(2fd314f3bab4cff449d02839dd31d41d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '171e672ab55f2ea873c9fd38b40fc2cf') in 0.0263495 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Roof_03C_3.fbx
  artifactKey: Guid(694778acc5eaec74482fb5ba71eab7be) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Roof_03C_3.fbx using Guid(694778acc5eaec74482fb5ba71eab7be) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '844788f7cec16adfe713705275e83a29') in 0.0249202 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.011435 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Floor_Small_04B_Open_Corner.fbx
  artifactKey: Guid(0be57da9539f0a841a8dfdb2ddad90f2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Floor_Small_04B_Open_Corner.fbx using Guid(0be57da9539f0a841a8dfdb2ddad90f2) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '36b71c0eed6794911ec6b5599a9629be') in 0.0254246 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Floor_Small_04C_Open_Corner.fbx
  artifactKey: Guid(3528f43424266184cb708a565a9367ff) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Floor_Small_04C_Open_Corner.fbx using Guid(3528f43424266184cb708a565a9367ff) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c54a63949f2d7c757d78598f53c79b5f') in 0.024358 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Floor_Small_05B.fbx
  artifactKey: Guid(1e840d1132de0ee40ba68011b583755a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Floor_Small_05B.fbx using Guid(1e840d1132de0ee40ba68011b583755a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '39f8de6bfbb8330eff966133d30caea0') in 0.026879 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Rail_01A.fbx
  artifactKey: Guid(4d535c4b14d92ef46b075ea6829447ae) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Rail_01A.fbx using Guid(4d535c4b14d92ef46b075ea6829447ae) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '488092385e142ed94ab9bf4dc6c2b11d') in 0.0253535 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000020 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Rail_03A.fbx
  artifactKey: Guid(ef9154916753b3d4f8404fad58b4a5e4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Rail_03A.fbx using Guid(ef9154916753b3d4f8404fad58b4a5e4) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'bdea0537350d62645c57153204794395') in 0.0254023 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Roof_01A_3.fbx
  artifactKey: Guid(aed052b342c72be48a0564686db0cb88) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Roof_01A_3.fbx using Guid(aed052b342c72be48a0564686db0cb88) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd0228efa8288f6cbec163182115bc73d') in 0.0287547 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Roof_01A_1.fbx
  artifactKey: Guid(d54b0281299ede94ba0a05146ee817e6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Roof_01A_1.fbx using Guid(d54b0281299ede94ba0a05146ee817e6) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '35e5f90788b4867461dcb5fb17346565') in 0.0231242 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/Toon Suburban Pack/Models/Colliders/TSP_House_Floor_Small_01A_Open_Side_MeshCollider.fbx
  artifactKey: Guid(c1b7e218ad8556849bfa1afc9b112a4f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/Colliders/TSP_House_Floor_Small_01A_Open_Side_MeshCollider.fbx using Guid(c1b7e218ad8556849bfa1afc9b112a4f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '548a49fee6bf6ea46bf5e9a7d1a1fdcb') in 0.024207 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000019 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Floor_Small_01C_Open_Corner.fbx
  artifactKey: Guid(33ea620ee34f67842b3026af4c8515ad) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Floor_Small_01C_Open_Corner.fbx using Guid(33ea620ee34f67842b3026af4c8515ad) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '3e4662729c15cca3ae9ece20873fdbf3') in 0.0270776 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Floor_Small_05A_Open_Center.fbx
  artifactKey: Guid(d41686c029d48a54ca7401741477f115) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Floor_Small_05A_Open_Center.fbx using Guid(d41686c029d48a54ca7401741477f115) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '5a53930d0226d3fb6af46384fef85843') in 0.0267683 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Floor_Small_02B_Open_Side.fbx
  artifactKey: Guid(d83c84aba03a86247bbda87cdad5cb6f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Floor_Small_02B_Open_Side.fbx using Guid(d83c84aba03a86247bbda87cdad5cb6f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ebe1e4ba58402ca56b77f01144c06deb') in 0.0264382 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Floor_Small_03B_Open_Side.fbx
  artifactKey: Guid(2225c3079f21309438265bed8846dfd8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Floor_Small_03B_Open_Side.fbx using Guid(2225c3079f21309438265bed8846dfd8) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ace132df3859ec93deedb238ab4c5944') in 0.0246202 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Floor_Small_03B_Open_Corner.fbx
  artifactKey: Guid(15cecf0f95343db49a7c636d7adc1166) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Floor_Small_03B_Open_Corner.fbx using Guid(15cecf0f95343db49a7c636d7adc1166) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'a9baceee752e9561719df9e8e562bfb0') in 0.0290319 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Floor_Regular_01A.fbx
  artifactKey: Guid(8a3fbd19d953a544b93de82efbb3e77d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Floor_Regular_01A.fbx using Guid(8a3fbd19d953a544b93de82efbb3e77d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '92998116da033d7692fc96c5a0fde8d7') in 0.026356 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Floor_Regular_02B.fbx
  artifactKey: Guid(0ae39a7011763294cb91a2e11f396848) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Floor_Regular_02B.fbx using Guid(0ae39a7011763294cb91a2e11f396848) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '70f2b3879b27cfe0440ed3ac851b1b59') in 0.0264133 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Floor_Regular_05B.fbx
  artifactKey: Guid(834b1acd0fc509642acc1dfaac4c727f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Floor_Regular_05B.fbx using Guid(834b1acd0fc509642acc1dfaac4c727f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e61af1229e07ccf193c2ecb14a5e9e4a') in 0.0253084 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Floor_Small_01A_Open_Corner.fbx
  artifactKey: Guid(f326828fbda9b9345ba9a503c71364eb) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Floor_Small_01A_Open_Corner.fbx using Guid(f326828fbda9b9345ba9a503c71364eb) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '7d6a094163e61c514a9306181f94a3de') in 0.0274839 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Floor_Regular_03C.fbx
  artifactKey: Guid(c01319f802618014c881536870d9ecb2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Floor_Regular_03C.fbx using Guid(c01319f802618014c881536870d9ecb2) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '8ebcfdea573f3baf3f6e335e07929008') in 0.025377 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Floor_Small_02A_Open_Side.fbx
  artifactKey: Guid(731fdfd748192e14c903b90e3f9a2a92) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Floor_Small_02A_Open_Side.fbx using Guid(731fdfd748192e14c903b90e3f9a2a92) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '111ae6c2f5a415714239db2c20a2f731') in 0.025258 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Floor_Small_03C.fbx
  artifactKey: Guid(73b6d68ce791ef54fa38d088605a2425) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Floor_Small_03C.fbx using Guid(73b6d68ce791ef54fa38d088605a2425) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'cea7a2365587f7637b515a2ab4c5c911') in 0.0258036 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.626626 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Grill_Fork_01A.fbx
  artifactKey: Guid(abe6fde2404dd46489a0ceabaa53bc46) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Grill_Fork_01A.fbx using Guid(abe6fde2404dd46489a0ceabaa53bc46) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '6b42031459ca2800be89f6a6438bf3dc') in 0.0245565 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Chimney_01A_Base.fbx
  artifactKey: Guid(f89e0157dca75ac43848bd9eb7deb4c0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Chimney_01A_Base.fbx using Guid(f89e0157dca75ac43848bd9eb7deb4c0) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'a76875b0654770bc873ff99b3ce38aff') in 0.0327241 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Toon Suburban Pack/Models/Colliders/TSP_Garbage_Truck_01A_Door.L_MeshCollider.fbx
  artifactKey: Guid(c83d8fc3f079b244aa7f85de8186d7ee) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/Colliders/TSP_Garbage_Truck_01A_Door.L_MeshCollider.fbx using Guid(c83d8fc3f079b244aa7f85de8186d7ee) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f8d03d68bbda12792e221d112218260f') in 0.0242733 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Garden_Ceiling_Canopy_01B.fbx
  artifactKey: Guid(5550397565d75ff4f9b2a255b1971a4c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Garden_Ceiling_Canopy_01B.fbx using Guid(5550397565d75ff4f9b2a255b1971a4c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e81aa39daf2a2841d63590b342b3858d') in 0.0280641 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Garden_Wall_Short_01B_Gate.fbx
  artifactKey: Guid(39fc5c3d980553845bac527a2d440b69) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Garden_Wall_Short_01B_Gate.fbx using Guid(39fc5c3d980553845bac527a2d440b69) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '7883cc544dacc5bce92ec1adeae3c40b') in 0.0252457 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Flower_04B.fbx
  artifactKey: Guid(0a03f096ec343f84cbef7ccbd2fbdfa8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Flower_04B.fbx using Guid(0a03f096ec343f84cbef7ccbd2fbdfa8) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '632a7f8c4db46c68752f05077670dd2c') in 0.0238101 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Garden_Ceiling_Small_01A.fbx
  artifactKey: Guid(9cd9063233a79f245812d01a2ebaccee) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Garden_Ceiling_Small_01A.fbx using Guid(9cd9063233a79f245812d01a2ebaccee) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b68f054f3d7ef0aecca4b5b9d5ac8017') in 0.031206 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/Toon Suburban Pack/Models/Colliders/TSP_Fence_Circular_01A_MeshCollider.fbx
  artifactKey: Guid(36937b4b1f4af314486638d290ef26ce) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/Colliders/TSP_Fence_Circular_01A_MeshCollider.fbx using Guid(36937b4b1f4af314486638d290ef26ce) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '503a1fe991aac87348472627dd70af49') in 0.0231744 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Fence_Green_01C.fbx
  artifactKey: Guid(7b79aa4812788f844b7ea1557262716c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Fence_Green_01C.fbx using Guid(7b79aa4812788f844b7ea1557262716c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '6024b9c7fcf2b947e452c3aa45bbf3d9') in 0.0342554 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 14

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Fence_Wood_02B_Pole.fbx
  artifactKey: Guid(cb033b5f8b080de499d2aa7ac67ae456) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Fence_Wood_02B_Pole.fbx using Guid(cb033b5f8b080de499d2aa7ac67ae456) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '23334e47804f2a40a888e0dd2c625b20') in 0.0269191 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Fence_Wood_02A_Pole.fbx
  artifactKey: Guid(529a2cdbc65ce2e41a47cff7556759d2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Fence_Wood_02A_Pole.fbx using Guid(529a2cdbc65ce2e41a47cff7556759d2) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '92a6fbed4ed8b204ecbdaa960e49502b') in 0.0254797 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000020 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Fence_Wood_02B.fbx
  artifactKey: Guid(87535f13632dc1b47874b307e0397f64) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Fence_Wood_02B.fbx using Guid(87535f13632dc1b47874b307e0397f64) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c6076690f43cadc3d4cff3e1fa3c40a3') in 0.0235637 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Exterior_Flower_Pot_01A.fbx
  artifactKey: Guid(c81c76eca7aae014f87558ba2c70a8fe) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Exterior_Flower_Pot_01A.fbx using Guid(c81c76eca7aae014f87558ba2c70a8fe) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '54a49e1fd5ebb33897e4900b24bc3fbb') in 0.0275071 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.005450 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Curtain_01A.L.fbx
  artifactKey: Guid(0438c70ceeea41d44bae5a617fba45d4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Curtain_01A.L.fbx using Guid(0438c70ceeea41d44bae5a617fba45d4) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '8a24658e60ade31d779aa6382c469bf6') in 0.0249028 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Curtain_01B.R.fbx
  artifactKey: Guid(f22317569343551429be0a50177e8e00) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Curtain_01B.R.fbx using Guid(f22317569343551429be0a50177e8e00) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f8d64571fdbae56461e6f5abdd1cc5f1') in 0.0231624 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Curtain_01D.fbx
  artifactKey: Guid(43b4dbb6f4106a243866c62a6e0ed791) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Curtain_01D.fbx using Guid(43b4dbb6f4106a243866c62a6e0ed791) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '2bf0135134feb284009fc6f1be32ee51') in 0.0248204 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Curtain_01E.L.fbx
  artifactKey: Guid(bcfc6ca6502666e4ca91890b6f072bac) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Curtain_01E.L.fbx using Guid(bcfc6ca6502666e4ca91890b6f072bac) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '1cbfcfc96fedae40beb9bb48608476cf') in 0.0243026 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Deck_01A.fbx
  artifactKey: Guid(aea0cbd52ee6f9741a97c4d92f79315f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Deck_01A.fbx using Guid(aea0cbd52ee6f9741a97c4d92f79315f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ae4b9964642f1a4b0ec3eb97731dd597') in 0.0278848 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Deck_Steps_01C.fbx
  artifactKey: Guid(72164be0f0a31bc458adca320eb2ab2c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Deck_Steps_01C.fbx using Guid(72164be0f0a31bc458adca320eb2ab2c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '9a440e689979cb54266430c3babb4511') in 0.036829 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Curtain_01C.R.fbx
  artifactKey: Guid(ba8a4b6bdcbb3994d9ca71619a8c96ca) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Curtain_01C.R.fbx using Guid(ba8a4b6bdcbb3994d9ca71619a8c96ca) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '545d81d5991d6b9f85260ef545891b22') in 0.0388828 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000019 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Concrete_Column_01A.fbx
  artifactKey: Guid(f878e45dc0f1be442a954ab339fe7de7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Concrete_Column_01A.fbx using Guid(f878e45dc0f1be442a954ab339fe7de7) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'bd453e584fcb92cc9ffdd9519d297ad7') in 0.026597 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Couch_02A.fbx
  artifactKey: Guid(30a7ccbbb4140514d9b4d867a4b73414) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Couch_02A.fbx using Guid(30a7ccbbb4140514d9b4d867a4b73414) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ee7f374ae5b3b8f24497287e28e50dfc') in 0.0251876 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/Toon Suburban Pack/Models/Colliders/TSP_Curb_Corner_02A_MeshCollider.fbx
  artifactKey: Guid(4fdde5943dc0e784a8652f9aef614f22) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/Colliders/TSP_Curb_Corner_02A_MeshCollider.fbx using Guid(4fdde5943dc0e784a8652f9aef614f22) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b97695ee9abfe59d8eccf2db62986a97') in 0.0267046 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Toon Suburban Pack/Models/Colliders/TSP_Curb_Straight_01A_MeshCollider.fbx
  artifactKey: Guid(2e8df79b858b040468ed66eb74cb551c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/Colliders/TSP_Curb_Straight_01A_MeshCollider.fbx using Guid(2e8df79b858b040468ed66eb74cb551c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '7712f94f9a7ae53ca8b340e2200e0d68') in 0.0235085 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Chandelier_02A.fbx
  artifactKey: Guid(032c2d5d55d7a764a857642960839914) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Chandelier_02A.fbx using Guid(032c2d5d55d7a764a857642960839914) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'bd77d1fc36511540056a36cb3aac851d') in 0.0358039 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 18

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Coffee_Table_01B.fbx
  artifactKey: Guid(d43af014e4b6bee429b55ec803ea670d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Coffee_Table_01B.fbx using Guid(d43af014e4b6bee429b55ec803ea670d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '9631a7eaf851bba2f8dcb71b6f51305b') in 0.02714 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000020 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Coffee_Mug_02A.fbx
  artifactKey: Guid(3c79c18dc62049a4ba6c387ab703177d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Coffee_Mug_02A.fbx using Guid(3c79c18dc62049a4ba6c387ab703177d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '85f5a315b0d28ba305a9710b6b0ef670') in 0.0278684 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Checkout_01A.fbx
  artifactKey: Guid(5777ab462fb0ffa419ad3ebfd7754090) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Checkout_01A.fbx using Guid(5777ab462fb0ffa419ad3ebfd7754090) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '5b97be531cc81946911b4de38f5560bf') in 0.0392747 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Couch_01C.fbx
  artifactKey: Guid(aa504aebb4f2f514896dd2ea2f5c9a20) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Couch_01C.fbx using Guid(aa504aebb4f2f514896dd2ea2f5c9a20) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f5b4b2547e2ef41f249d749af24e3a38') in 0.0283985 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.097752 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Deck_Steps_02B.fbx
  artifactKey: Guid(6370cdaee28333149aecc37c6f7cfff4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Deck_Steps_02B.fbx using Guid(6370cdaee28333149aecc37c6f7cfff4) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'dedd37dd6e74f490aee06ea1c19f5bd8') in 0.0256246 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.452904 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Flower_01D.fbx
  artifactKey: Guid(4691ee60beb690f43a6e9187ff7fe0b1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Flower_01D.fbx using Guid(4691ee60beb690f43a6e9187ff7fe0b1) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'cd9569f517929325843ce3f92de56713') in 0.0248728 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Flower_02C.fbx
  artifactKey: Guid(6e50787659e9cbc4aacad892d019844e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Flower_02C.fbx using Guid(6e50787659e9cbc4aacad892d019844e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '95ea0e0833a4b23d7e4d67ad90e0f06f') in 0.0241882 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.005398 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Flower_03B.fbx
  artifactKey: Guid(017692c1696c05a4a8c5d87d5cd83fe5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Flower_03B.fbx using Guid(017692c1696c05a4a8c5d87d5cd83fe5) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '07de3cfe0134b761b548ede54e1ca90a') in 0.024114 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorkerClient::OnTransportError - code=2 error=End of file
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0