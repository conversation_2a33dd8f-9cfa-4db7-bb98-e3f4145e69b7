Using pre-set license
Built from '6000.0/staging' branch; Version is '6000.0.47f1 (2ad1ed33fd3b) revision 2806253'; Using compiler version '193933523'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit CoreSingleLanguage' Language: 'fr' Physical Memory: 16051 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker24
-projectPath
C:/Users/<USER>/ONU
-logFile
Logs/AssetImportWorker24.log
-srvPort
56204
-job-worker-count
7
-background-job-worker-count
8
-gc-helper-count
1
Successfully changed project path to: C:/Users/<USER>/ONU
C:/Users/<USER>/ONU
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [15024]  Target information:

Player connection [15024]  * "[IP] ************** [Port] 0 [Flags] 2 [Guid] 3612833146 [EditorId] 3612833146 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-34E5P9N) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [15024] Host joined multi-casting on [***********:54997]...
Player connection [15024] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 7
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 302.81 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.0.47f1 (2ad1ed33fd3b)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path C:/Users/<USER>/ONU/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce GTX 1650 (ID=0x1f9d)
    Vendor:   NVIDIA
    VRAM:     3937 MB
    Driver:   32.0.15.7602
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56340
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Registered in 0.003146 seconds.
- Loaded All Assemblies, in  1.086 seconds
Native extension for Android target not found
CommandWithNoStdoutInvokationFailure: Unable to start ADB server. Please make sure the Android SDK is installed and is properly configured in the Editor. See the Console for more details.
  at UnityEditor.Android.Command.RunWithShellExecute (System.String command, System.String args, System.String workingdir, System.String errorMsg) [0x0005a] in /home/<USER>/build/output/unity/unity/PlatformDependent/AndroidPlayer/Editor/Managed/Android/Command.cs:148 
  at UnityEditor.Android.ADB.StartServer () [0x00008] in /home/<USER>/build/output/unity/unity/PlatformDependent/AndroidPlayer/Editor/Managed/Android/ADB.cs:176 
  at UnityEditor.Android.ADB.Run (System.String[] command, UnityEditor.Android.Command+WaitingForProcessToExit waitingForProcessToExit, System.String errorMsg) [0x0000b] in /home/<USER>/build/output/unity/unity/PlatformDependent/AndroidPlayer/Editor/Managed/Android/ADB.cs:214 
  at UnityEditor.Android.AndroidDeploymentTargetsExtension.GetKnownTargets (UnityEditor.DeploymentTargets.IDeploymentTargetsMainThreadContext context, UnityEditor.ProgressHandler progressHandler) [0x002a7] in /home/<USER>/build/output/unity/unity/PlatformDependent/AndroidPlayer/Editor/Managed/DeploymentTargets/AndroidDeploymentTargetsExtension.cs:357 
  at UnityEditor.Android.TargetScanWorker.ScanSync () [0x00049] in /home/<USER>/build/output/unity/unity/PlatformDependent/AndroidPlayer/Editor/Managed/TargetScanWorker.cs:164 
  at UnityEditor.Android.TargetExtension.OnUsbDevicesChanged (UnityEditor.Hardware.UsbDevice[] usbDevices) [0x00087] in /home/<USER>/build/output/unity/unity/PlatformDependent/AndroidPlayer/Editor/Managed/ExtensionModule.cs:106 
  at UnityEditor.Android.TargetExtension.OnLoad () [0x000af] in /home/<USER>/build/output/unity/unity/PlatformDependent/AndroidPlayer/Editor/Managed/ExtensionModule.cs:67 
  at UnityEditor.Modules.ModuleManager.InitializePlatformSupportModules () [0x000ba] in <f8e1f14ef858402482f58cef2e987b5b>:0 
UnityEngine.DebugLogHandler:Internal_LogException_Injected(Exception, IntPtr)
UnityEngine.DebugLogHandler:Internal_LogException(Exception, Object)
UnityEngine.DebugLogHandler:LogException(Exception, Object)
UnityEngine.Logger:LogException(Exception, Object)
UnityEngine.Debug:LogException(Exception)
UnityEditor.Modules.ModuleManager:InitializePlatformSupportModules()

(Filename: /home/<USER>/build/output/unity/unity/PlatformDependent/AndroidPlayer/Editor/Managed/Android/Command.cs Line: 148)

Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  7.921 seconds
Domain Reload Profiling: 9006ms
	BeginReloadAssembly (118ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (36ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (41ms)
	LoadAllAssembliesAndSetupDomain (880ms)
		LoadAssemblies (117ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (877ms)
			TypeCache.Refresh (876ms)
				TypeCache.ScanAssembly (866ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (0ms)
	FinalizeReload (7921ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (7886ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (7707ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (41ms)
			ProcessInitializeOnLoadAttributes (85ms)
			ProcessInitializeOnLoadMethodAttributes (48ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.- Loaded All Assemblies, in  0.645 seconds
Refreshing native plugins compatible for Editor in 2.16 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
Package Manager log level set to [2]
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  0.582 seconds
Domain Reload Profiling: 1225ms
	BeginReloadAssembly (135ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (23ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (31ms)
	LoadAllAssembliesAndSetupDomain (441ms)
		LoadAssemblies (305ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (216ms)
			TypeCache.Refresh (158ms)
				TypeCache.ScanAssembly (142ms)
			BuildScriptInfoCaches (46ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (583ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (444ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (9ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (81ms)
			ProcessInitializeOnLoadAttributes (286ms)
			ProcessInitializeOnLoadMethodAttributes (61ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.02 seconds
Refreshing native plugins compatible for Editor in 3.85 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 200 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6553 unused Assets / (6.9 MB). Loaded Objects now: 7198.
Memory consumption went from 171.5 MB to 164.6 MB.
Total: 11.438200 ms (FindLiveObjects: 1.006100 ms CreateObjectMapping: 0.789400 ms MarkObjects: 5.661200 ms  DeleteObjects: 3.978000 ms)

========================================================================
Received Import Request.
  Time since last request: 1488828.320584 seconds.
  path: Assets/StarterAssets/ThirdPersonController/Character/Animations/Entering Car.fbx
  artifactKey: Guid(77f447b3583cedc4c84f415d211e885d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/StarterAssets/ThirdPersonController/Character/Animations/Entering Car.fbx using Guid(77f447b3583cedc4c84f415d211e885d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '18ed68cf1998f03b99880fdc62509182') in 0.0584515 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 135

========================================================================
Received Import Request.
  Time since last request: 8.581464 seconds.
  path: Assets/StarterAssets/ThirdPersonController/Character/Animations/mixamo.com.anim
  artifactKey: Guid(37a9a31cc9865bf4eaf5af8534046e0a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/StarterAssets/ThirdPersonController/Character/Animations/mixamo.com.anim using Guid(37a9a31cc9865bf4eaf5af8534046e0a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '8018203adda5236fcdcf01821fd8f4c4') in 0.011353 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 9.163711 seconds.
  path: Assets/StarterAssets/ThirdPersonController/Character/Animations/EnterCar.anim
  artifactKey: Guid(37a9a31cc9865bf4eaf5af8534046e0a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/StarterAssets/ThirdPersonController/Character/Animations/EnterCar.anim using Guid(37a9a31cc9865bf4eaf5af8534046e0a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '2fa2cb0b02a44d31d1d3e65f41ac371d') in 0.0095765 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 41.102482 seconds.
  path: Assets/StarterAssets/ThirdPersonController/Character/Models/Armature.fbx
  artifactKey: Guid(36078ab0369161e49a29d349ae3e0739) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/StarterAssets/ThirdPersonController/Character/Models/Armature.fbx using Guid(36078ab0369161e49a29d349ae3e0739) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '671b5c2eff7d64fb9fb5447d731bbf0c') in 0.6227628 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 151

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Female_Character_03.fbx
  artifactKey: Guid(cd840b40de5e2714fab2ddbee646e0f6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Female_Character_03.fbx using Guid(cd840b40de5e2714fab2ddbee646e0f6) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'cc06c24ea19af773a311c6b663ed895c') in 0.2159201 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 188

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Male_Character_02.fbx
  artifactKey: Guid(4cfe309f6ec3c2740b27e2366cb3f343) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Male_Character_02.fbx using Guid(4cfe309f6ec3c2740b27e2366cb3f343) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '83f989cb924778a365213b007ffd82d1') in 0.1766911 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 164

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Male_Character_03.fbx
  artifactKey: Guid(4cb752ca97a0cc148a69df7e9a5f23a4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Male_Character_03.fbx using Guid(4cb752ca97a0cc148a69df7e9a5f23a4) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '658bf83561a65287fd1221c79b0dc2a9') in 0.1639046 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 178

========================================================================
Received Import Request.
  Time since last request: 51.421307 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Kitchen_Chair_01B.fbx
  artifactKey: Guid(0fd49e7bfa1e4d0448a5a794673d0084) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Kitchen_Chair_01B.fbx using Guid(0fd49e7bfa1e4d0448a5a794673d0084) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd074a7af8103c61556df23acfb933dec') in 0.0297202 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Lamp_01B.fbx
  artifactKey: Guid(3c6757aed09ddbf4d890380fc5c0a65f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Lamp_01B.fbx using Guid(3c6757aed09ddbf4d890380fc5c0a65f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b5cd3a46603850a9e869505ceb164c2a') in 0.0435875 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 18

========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Microwave_01A.fbx
  artifactKey: Guid(2e2d9a915dd390e4194d56ce6e2b7db2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Microwave_01A.fbx using Guid(2e2d9a915dd390e4194d56ce6e2b7db2) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b8220ef27d3a5014884be95ef0b01532') in 0.0714209 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 18

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Motorcycle_01B.fbx
  artifactKey: Guid(fc3bfb4fc3e84884f8d38c79db94264e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Motorcycle_01B.fbx using Guid(fc3bfb4fc3e84884f8d38c79db94264e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd4309c9eab4f1577257f50ef057b9ef1') in 0.1307574 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 50

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Motorcycle_01A.fbx
  artifactKey: Guid(4ea2174be71e64e438bc4253f9c4e97d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Motorcycle_01A.fbx using Guid(4ea2174be71e64e438bc4253f9c4e97d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '5843dc29e7b3435fccaa85d18bcbe78d') in 0.0824063 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 50

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Lounge_Chair_02A.fbx
  artifactKey: Guid(dfefd41b6a6fbf84f84f58bddd523685) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Lounge_Chair_02A.fbx using Guid(dfefd41b6a6fbf84f84f58bddd523685) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '32b9e710666b13d5ce66a57e9985812f') in 0.0230679 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Lamp_01A.fbx
  artifactKey: Guid(163fe9577a057174cbc2f69ff9e8617d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Lamp_01A.fbx using Guid(163fe9577a057174cbc2f69ff9e8617d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ec0d182f9fa427ff8006296452068c8c') in 0.033743 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 18

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Nightstand_01B.fbx
  artifactKey: Guid(bce2c0630fc73cf4aa3fc07297db2207) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Nightstand_01B.fbx using Guid(bce2c0630fc73cf4aa3fc07297db2207) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '93a35e621355c982459ec751c43e356d') in 0.0236353 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Minivan_01C.fbx
  artifactKey: Guid(b932dc4abb7a36744a7364fe90f0bb16) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Minivan_01C.fbx using Guid(b932dc4abb7a36744a7364fe90f0bb16) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b27fe177db86dd26c699dd3ad0c34554') in 0.1234066 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 76

Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0