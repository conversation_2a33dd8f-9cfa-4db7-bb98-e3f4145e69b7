Using pre-set license
Built from '6000.0/staging' branch; Version is '6000.0.47f1 (2ad1ed33fd3b) revision 2806253'; Using compiler version '193933523'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit CoreSingleLanguage' Language: 'fr' Physical Memory: 16051 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker5
-projectPath
C:/Users/<USER>/ONU
-logFile
Logs/AssetImportWorker5.log
-srvPort
56204
-job-worker-count
7
-background-job-worker-count
8
-gc-helper-count
1
Successfully changed project path to: C:/Users/<USER>/ONU
C:/Users/<USER>/ONU
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [33636]  Target information:

Player connection [33636]  * "[IP] ************** [Port] 0 [Flags] 2 [Guid] 1977709812 [EditorId] 1977709812 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-34E5P9N) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [33636] Host joined multi-casting on [***********:54997]...
Player connection [33636] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 7
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 3.94 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.0.47f1 (2ad1ed33fd3b)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path C:/Users/<USER>/ONU/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce GTX 1650 (ID=0x1f9d)
    Vendor:   NVIDIA
    VRAM:     3937 MB
    Driver:   32.0.15.7602
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56488
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Registered in 0.003763 seconds.
- Loaded All Assemblies, in  0.479 seconds
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 305 ms
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.767 seconds
Domain Reload Profiling: 1244ms
	BeginReloadAssembly (181ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (37ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (55ms)
	LoadAllAssembliesAndSetupDomain (193ms)
		LoadAssemblies (175ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (188ms)
			TypeCache.Refresh (187ms)
				TypeCache.ScanAssembly (168ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (0ms)
	FinalizeReload (768ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (719ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (413ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (65ms)
			ProcessInitializeOnLoadAttributes (155ms)
			ProcessInitializeOnLoadMethodAttributes (82ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.964 seconds
Refreshing native plugins compatible for Editor in 3.72 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
Package Manager log level set to [2]
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  0.909 seconds
Domain Reload Profiling: 1870ms
	BeginReloadAssembly (226ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (49ms)
	RebuildCommonClasses (40ms)
	RebuildNativeTypeToScriptingClass (13ms)
	initialDomainReloadingComplete (43ms)
	LoadAllAssembliesAndSetupDomain (638ms)
		LoadAssemblies (469ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (304ms)
			TypeCache.Refresh (223ms)
				TypeCache.ScanAssembly (202ms)
			BuildScriptInfoCaches (65ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (909ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (653ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (18ms)
			SetLoadedEditorAssemblies (8ms)
			BeforeProcessingInitializeOnLoad (129ms)
			ProcessInitializeOnLoadAttributes (396ms)
			ProcessInitializeOnLoadMethodAttributes (98ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.02 seconds
Refreshing native plugins compatible for Editor in 3.23 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 192 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6191 unused Assets / (6.8 MB). Loaded Objects now: 6825.
Memory consumption went from 161.0 MB to 154.3 MB.
Total: 13.968200 ms (FindLiveObjects: 1.710400 ms CreateObjectMapping: 0.929500 ms MarkObjects: 6.799500 ms  DeleteObjects: 4.521400 ms)

========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 3.03 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 32 Unused Serialized files (Serialized files now loaded: 0)
Unloading 32 unused Assets / (3.0 MB). Loaded Objects now: 6825.
Memory consumption went from 131.0 MB to 128.0 MB.
Total: 8.324300 ms (FindLiveObjects: 0.379700 ms CreateObjectMapping: 0.400900 ms MarkObjects: 6.382000 ms  DeleteObjects: 1.160100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.645 seconds
Refreshing native plugins compatible for Editor in 1.65 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  0.613 seconds
Domain Reload Profiling: 1258ms
	BeginReloadAssembly (169ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (41ms)
	RebuildCommonClasses (31ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (23ms)
	LoadAllAssembliesAndSetupDomain (411ms)
		LoadAssemblies (307ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (188ms)
			TypeCache.Refresh (92ms)
				TypeCache.ScanAssembly (82ms)
			BuildScriptInfoCaches (84ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (614ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (438ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (10ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (86ms)
			ProcessInitializeOnLoadAttributes (283ms)
			ProcessInitializeOnLoadMethodAttributes (52ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 3.08 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 45 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6206 unused Assets / (7.4 MB). Loaded Objects now: 6851.
Memory consumption went from 150.8 MB to 143.4 MB.
Total: 13.034600 ms (FindLiveObjects: 0.680900 ms CreateObjectMapping: 0.806200 ms MarkObjects: 6.315400 ms  DeleteObjects: 5.229600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 1482590.081898 seconds.
  path: Assets/TutorialInfo/Icons/Mobile 3D.png
  artifactKey: Guid(cbea0258d69fdef4ba73fa582f0e2004) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/TutorialInfo/Icons/Mobile 3D.png using Guid(cbea0258d69fdef4ba73fa582f0e2004) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '92b33e4246a0b7335a395dda2d39153e') in 0.118151 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 23.403721 seconds.
  path: Assets/Settings/Mobile_Renderer.asset
  artifactKey: Guid(65bc7dbf4170f435aa868c779acfb082) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Settings/Mobile_Renderer.asset using Guid(65bc7dbf4170f435aa868c779acfb082) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'a200c5b489e2ba3068edb0e8d1ae3e52') in 0.0060056 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000013 seconds.
  path: Assets/Settings/PC_RPAsset.asset
  artifactKey: Guid(4b83569d67af61e458304325a23e5dfd) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Settings/PC_RPAsset.asset using Guid(4b83569d67af61e458304325a23e5dfd) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f920ee52e08e9c71496953f432d613b4') in 0.0129555 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 0.000014 seconds.
  path: Assets/Settings/UniversalRenderPipelineGlobalSettings.asset
  artifactKey: Guid(18dc0cd2c080841dea60987a38ce93fa) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Settings/UniversalRenderPipelineGlobalSettings.asset using Guid(18dc0cd2c080841dea60987a38ce93fa) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'acf67e02c8f89321dec212247a033c36') in 0.0009764 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0