Using pre-set license
Built from '6000.0/staging' branch; Version is '6000.0.47f1 (2ad1ed33fd3b) revision 2806253'; Using compiler version '193933523'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit CoreSingleLanguage' Language: 'fr' Physical Memory: 16051 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker28
-projectPath
C:/Users/<USER>/ONU
-logFile
Logs/AssetImportWorker28.log
-srvPort
56204
-job-worker-count
7
-background-job-worker-count
8
-gc-helper-count
1
Successfully changed project path to: C:/Users/<USER>/ONU
C:/Users/<USER>/ONU
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [45840]  Target information:

Player connection [45840]  * "[IP] ************** [Port] 0 [Flags] 2 [Guid] 93422964 [EditorId] 93422964 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-34E5P9N) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [45840] Host joined multi-casting on [***********:54997]...
Player connection [45840] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 7
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 1.85 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.0.47f1 (2ad1ed33fd3b)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path C:/Users/<USER>/ONU/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce GTX 1650 (ID=0x1f9d)
    Vendor:   NVIDIA
    VRAM:     3937 MB
    Driver:   32.0.15.7602
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56924
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Registered in 0.003577 seconds.
- Loaded All Assemblies, in  0.354 seconds
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 235 ms
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.550 seconds
Domain Reload Profiling: 903ms
	BeginReloadAssembly (118ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (35ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (46ms)
	LoadAllAssembliesAndSetupDomain (143ms)
		LoadAssemblies (116ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (141ms)
			TypeCache.Refresh (139ms)
				TypeCache.ScanAssembly (127ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (0ms)
	FinalizeReload (551ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (511ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (315ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (43ms)
			ProcessInitializeOnLoadAttributes (101ms)
			ProcessInitializeOnLoadMethodAttributes (49ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.- Loaded All Assemblies, in  0.647 seconds
Refreshing native plugins compatible for Editor in 1.49 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
Package Manager log level set to [2]
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  0.637 seconds
Domain Reload Profiling: 1281ms
	BeginReloadAssembly (129ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (23ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (31ms)
	LoadAllAssembliesAndSetupDomain (448ms)
		LoadAssemblies (316ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (208ms)
			TypeCache.Refresh (154ms)
				TypeCache.ScanAssembly (140ms)
			BuildScriptInfoCaches (42ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (637ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (503ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (9ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (82ms)
			ProcessInitializeOnLoadAttributes (338ms)
			ProcessInitializeOnLoadMethodAttributes (65ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (5ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.02 seconds
Refreshing native plugins compatible for Editor in 3.18 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 200 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6553 unused Assets / (6.8 MB). Loaded Objects now: 7198.
Memory consumption went from 175.1 MB to 168.4 MB.
Total: 12.421300 ms (FindLiveObjects: 1.434500 ms CreateObjectMapping: 0.841600 ms MarkObjects: 5.761300 ms  DeleteObjects: 4.380900 ms)

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.- Loaded All Assemblies, in  1.347 seconds
Refreshing native plugins compatible for Editor in 6.80 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  1.445 seconds
Domain Reload Profiling: 2786ms
	BeginReloadAssembly (335ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (69ms)
	RebuildCommonClasses (76ms)
	RebuildNativeTypeToScriptingClass (29ms)
	initialDomainReloadingComplete (62ms)
	LoadAllAssembliesAndSetupDomain (838ms)
		LoadAssemblies (654ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (359ms)
			TypeCache.Refresh (16ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (314ms)
			ResolveRequiredComponents (23ms)
	FinalizeReload (1446ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1033ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (21ms)
			SetLoadedEditorAssemblies (10ms)
			BeforeProcessingInitializeOnLoad (199ms)
			ProcessInitializeOnLoadAttributes (682ms)
			ProcessInitializeOnLoadMethodAttributes (110ms)
			AfterProcessingInitializeOnLoad (10ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (26ms)
Refreshing native plugins compatible for Editor in 5.21 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 43 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6551 unused Assets / (7.2 MB). Loaded Objects now: 7213.
Memory consumption went from 162.7 MB to 155.5 MB.
Total: 14.568500 ms (FindLiveObjects: 1.179600 ms CreateObjectMapping: 1.407900 ms MarkObjects: 7.251200 ms  DeleteObjects: 4.728100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.- Loaded All Assemblies, in  0.768 seconds
Refreshing native plugins compatible for Editor in 1.60 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  0.593 seconds
Domain Reload Profiling: 1362ms
	BeginReloadAssembly (332ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (14ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (71ms)
	RebuildCommonClasses (40ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (23ms)
	LoadAllAssembliesAndSetupDomain (364ms)
		LoadAssemblies (386ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (153ms)
			TypeCache.Refresh (9ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (132ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (593ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (433ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (10ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (79ms)
			ProcessInitializeOnLoadAttributes (283ms)
			ProcessInitializeOnLoadMethodAttributes (53ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (14ms)
Refreshing native plugins compatible for Editor in 2.87 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 33 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6551 unused Assets / (7.1 MB). Loaded Objects now: 7215.
Memory consumption went from 161.0 MB to 154.0 MB.
Total: 13.493100 ms (FindLiveObjects: 0.685100 ms CreateObjectMapping: 0.935900 ms MarkObjects: 6.923500 ms  DeleteObjects: 4.946600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.- Loaded All Assemblies, in  0.580 seconds
Refreshing native plugins compatible for Editor in 1.62 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  0.591 seconds
Domain Reload Profiling: 1171ms
	BeginReloadAssembly (158ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (40ms)
	RebuildCommonClasses (27ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (21ms)
	LoadAllAssembliesAndSetupDomain (363ms)
		LoadAssemblies (287ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (153ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (134ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (592ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (438ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (9ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (91ms)
			ProcessInitializeOnLoadAttributes (284ms)
			ProcessInitializeOnLoadMethodAttributes (47ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 3.07 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 33 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6551 unused Assets / (7.1 MB). Loaded Objects now: 7217.
Memory consumption went from 161.0 MB to 154.0 MB.
Total: 12.694900 ms (FindLiveObjects: 0.763500 ms CreateObjectMapping: 0.802800 ms MarkObjects: 6.625800 ms  DeleteObjects: 4.500500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.- Loaded All Assemblies, in  0.687 seconds
Refreshing native plugins compatible for Editor in 1.55 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  0.604 seconds
Domain Reload Profiling: 1291ms
	BeginReloadAssembly (272ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (15ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (64ms)
	RebuildCommonClasses (27ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (23ms)
	LoadAllAssembliesAndSetupDomain (355ms)
		LoadAssemblies (332ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (150ms)
			TypeCache.Refresh (8ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (129ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (604ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (447ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (12ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (88ms)
			ProcessInitializeOnLoadAttributes (300ms)
			ProcessInitializeOnLoadMethodAttributes (40ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 2.91 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 33 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6551 unused Assets / (7.0 MB). Loaded Objects now: 7219.
Memory consumption went from 161.1 MB to 154.0 MB.
Total: 11.882700 ms (FindLiveObjects: 0.662000 ms CreateObjectMapping: 0.829800 ms MarkObjects: 5.936900 ms  DeleteObjects: 4.452200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.- Loaded All Assemblies, in  0.585 seconds
Refreshing native plugins compatible for Editor in 1.75 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  0.583 seconds
Domain Reload Profiling: 1169ms
	BeginReloadAssembly (163ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (37ms)
	RebuildCommonClasses (27ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (364ms)
		LoadAssemblies (292ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (151ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (131ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (584ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (433ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (9ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (96ms)
			ProcessInitializeOnLoadAttributes (280ms)
			ProcessInitializeOnLoadMethodAttributes (40ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 2.82 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 33 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6551 unused Assets / (7.0 MB). Loaded Objects now: 7221.
Memory consumption went from 161.0 MB to 154.0 MB.
Total: 12.675700 ms (FindLiveObjects: 0.732300 ms CreateObjectMapping: 0.944100 ms MarkObjects: 6.239700 ms  DeleteObjects: 4.757900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 3.13 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 33 Unused Serialized files (Serialized files now loaded: 0)
Unloading 33 unused Assets / (3.1 MB). Loaded Objects now: 7221.
Memory consumption went from 157.0 MB to 153.9 MB.
Total: 9.389200 ms (FindLiveObjects: 0.370200 ms CreateObjectMapping: 0.364800 ms MarkObjects: 6.948600 ms  DeleteObjects: 1.704000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.- Loaded All Assemblies, in  1.752 seconds
Refreshing native plugins compatible for Editor in 1.66 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  0.591 seconds
Domain Reload Profiling: 2341ms
	BeginReloadAssembly (1089ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (14ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (514ms)
	RebuildCommonClasses (53ms)
	RebuildNativeTypeToScriptingClass (20ms)
	initialDomainReloadingComplete (48ms)
	LoadAllAssembliesAndSetupDomain (539ms)
		LoadAssemblies (611ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (210ms)
			TypeCache.Refresh (11ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (186ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (592ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (437ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (9ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (81ms)
			ProcessInitializeOnLoadAttributes (285ms)
			ProcessInitializeOnLoadMethodAttributes (56ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 3.01 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 33 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6551 unused Assets / (7.0 MB). Loaded Objects now: 7223.
Memory consumption went from 161.1 MB to 154.1 MB.
Total: 12.248200 ms (FindLiveObjects: 0.677200 ms CreateObjectMapping: 0.860200 ms MarkObjects: 6.259700 ms  DeleteObjects: 4.449300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.- Loaded All Assemblies, in  0.577 seconds
Refreshing native plugins compatible for Editor in 1.63 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  0.609 seconds
Domain Reload Profiling: 1186ms
	BeginReloadAssembly (156ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (8ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (36ms)
	RebuildCommonClasses (30ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (359ms)
		LoadAssemblies (282ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (153ms)
			TypeCache.Refresh (9ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (130ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (610ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (456ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (10ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (90ms)
			ProcessInitializeOnLoadAttributes (304ms)
			ProcessInitializeOnLoadMethodAttributes (45ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 2.76 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 33 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6551 unused Assets / (6.9 MB). Loaded Objects now: 7225.
Memory consumption went from 161.1 MB to 154.1 MB.
Total: 11.734300 ms (FindLiveObjects: 0.718900 ms CreateObjectMapping: 0.861300 ms MarkObjects: 6.005700 ms  DeleteObjects: 4.146200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.- Loaded All Assemblies, in  0.592 seconds
Refreshing native plugins compatible for Editor in 2.01 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  0.599 seconds
Domain Reload Profiling: 1192ms
	BeginReloadAssembly (162ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (40ms)
	RebuildCommonClasses (27ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (21ms)
	LoadAllAssembliesAndSetupDomain (373ms)
		LoadAssemblies (291ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (160ms)
			TypeCache.Refresh (9ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (138ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (600ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (443ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (9ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (87ms)
			ProcessInitializeOnLoadAttributes (286ms)
			ProcessInitializeOnLoadMethodAttributes (52ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Refreshing native plugins compatible for Editor in 3.21 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 33 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6551 unused Assets / (7.2 MB). Loaded Objects now: 7227.
Memory consumption went from 161.1 MB to 153.8 MB.
Total: 11.738200 ms (FindLiveObjects: 0.675700 ms CreateObjectMapping: 0.798500 ms MarkObjects: 5.642200 ms  DeleteObjects: 4.620500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.- Loaded All Assemblies, in  0.579 seconds
Refreshing native plugins compatible for Editor in 1.47 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  0.593 seconds
Domain Reload Profiling: 1172ms
	BeginReloadAssembly (156ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (38ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (23ms)
	LoadAllAssembliesAndSetupDomain (361ms)
		LoadAssemblies (282ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (155ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (134ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (593ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (438ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (9ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (85ms)
			ProcessInitializeOnLoadAttributes (285ms)
			ProcessInitializeOnLoadMethodAttributes (52ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 3.37 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 33 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6551 unused Assets / (7.0 MB). Loaded Objects now: 7229.
Memory consumption went from 161.1 MB to 154.1 MB.
Total: 12.424800 ms (FindLiveObjects: 0.818000 ms CreateObjectMapping: 0.941700 ms MarkObjects: 6.457300 ms  DeleteObjects: 4.205200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.- Loaded All Assemblies, in  0.593 seconds
Refreshing native plugins compatible for Editor in 1.76 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  0.590 seconds
Domain Reload Profiling: 1184ms
	BeginReloadAssembly (162ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (40ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (371ms)
		LoadAssemblies (293ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (154ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (135ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (590ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (434ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (9ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (86ms)
			ProcessInitializeOnLoadAttributes (284ms)
			ProcessInitializeOnLoadMethodAttributes (47ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 3.03 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 33 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6551 unused Assets / (7.0 MB). Loaded Objects now: 7231.
Memory consumption went from 161.0 MB to 154.0 MB.
Total: 12.406000 ms (FindLiveObjects: 0.884100 ms CreateObjectMapping: 0.842700 ms MarkObjects: 6.359100 ms  DeleteObjects: 4.318000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.- Loaded All Assemblies, in  0.612 seconds
Refreshing native plugins compatible for Editor in 1.48 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  0.663 seconds
Domain Reload Profiling: 1276ms
	BeginReloadAssembly (164ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (40ms)
	RebuildCommonClasses (34ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (23ms)
	LoadAllAssembliesAndSetupDomain (382ms)
		LoadAssemblies (295ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (166ms)
			TypeCache.Refresh (9ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (143ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (663ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (504ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (9ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (96ms)
			ProcessInitializeOnLoadAttributes (323ms)
			ProcessInitializeOnLoadMethodAttributes (68ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (15ms)
Refreshing native plugins compatible for Editor in 3.39 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 33 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6551 unused Assets / (7.1 MB). Loaded Objects now: 7233.
Memory consumption went from 161.1 MB to 153.9 MB.
Total: 13.815100 ms (FindLiveObjects: 1.087500 ms CreateObjectMapping: 1.076600 ms MarkObjects: 6.471600 ms  DeleteObjects: 5.177600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.- Loaded All Assemblies, in  0.589 seconds
Refreshing native plugins compatible for Editor in 1.54 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  0.612 seconds
Domain Reload Profiling: 1202ms
	BeginReloadAssembly (160ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (40ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (370ms)
		LoadAssemblies (290ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (157ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (138ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (612ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (458ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (9ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (87ms)
			ProcessInitializeOnLoadAttributes (297ms)
			ProcessInitializeOnLoadMethodAttributes (56ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 3.13 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 33 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6551 unused Assets / (7.0 MB). Loaded Objects now: 7235.
Memory consumption went from 161.1 MB to 154.1 MB.
Total: 12.411400 ms (FindLiveObjects: 0.697700 ms CreateObjectMapping: 0.805000 ms MarkObjects: 6.165600 ms  DeleteObjects: 4.741600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.- Loaded All Assemblies, in  0.636 seconds
Refreshing native plugins compatible for Editor in 3.05 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  0.671 seconds
Domain Reload Profiling: 1308ms
	BeginReloadAssembly (162ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (36ms)
	RebuildCommonClasses (32ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (24ms)
	LoadAllAssembliesAndSetupDomain (409ms)
		LoadAssemblies (301ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (188ms)
			TypeCache.Refresh (9ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (158ms)
			ResolveRequiredComponents (16ms)
	FinalizeReload (671ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (509ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (21ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (104ms)
			ProcessInitializeOnLoadAttributes (325ms)
			ProcessInitializeOnLoadMethodAttributes (48ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 3.39 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 33 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6551 unused Assets / (6.7 MB). Loaded Objects now: 7237.
Memory consumption went from 161.1 MB to 154.4 MB.
Total: 12.291100 ms (FindLiveObjects: 0.718400 ms CreateObjectMapping: 0.959700 ms MarkObjects: 6.438400 ms  DeleteObjects: 4.172900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.- Loaded All Assemblies, in  0.611 seconds
Refreshing native plugins compatible for Editor in 1.62 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  0.657 seconds
Domain Reload Profiling: 1270ms
	BeginReloadAssembly (160ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (38ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (23ms)
	LoadAllAssembliesAndSetupDomain (391ms)
		LoadAssemblies (289ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (178ms)
			TypeCache.Refresh (9ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (154ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (658ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (498ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (10ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (112ms)
			ProcessInitializeOnLoadAttributes (318ms)
			ProcessInitializeOnLoadMethodAttributes (49ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 3.19 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 33 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6551 unused Assets / (7.1 MB). Loaded Objects now: 7239.
Memory consumption went from 161.1 MB to 154.0 MB.
Total: 12.387000 ms (FindLiveObjects: 0.758300 ms CreateObjectMapping: 0.891100 ms MarkObjects: 6.065500 ms  DeleteObjects: 4.669900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.- Loaded All Assemblies, in  0.584 seconds
Refreshing native plugins compatible for Editor in 1.68 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  0.597 seconds
Domain Reload Profiling: 1183ms
	BeginReloadAssembly (161ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (41ms)
	RebuildCommonClasses (27ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (23ms)
	LoadAllAssembliesAndSetupDomain (364ms)
		LoadAssemblies (286ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (157ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (137ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (597ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (444ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (10ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (89ms)
			ProcessInitializeOnLoadAttributes (294ms)
			ProcessInitializeOnLoadMethodAttributes (44ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 2.95 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 33 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6551 unused Assets / (7.0 MB). Loaded Objects now: 7241.
Memory consumption went from 161.0 MB to 154.0 MB.
Total: 12.958800 ms (FindLiveObjects: 0.934700 ms CreateObjectMapping: 1.451700 ms MarkObjects: 6.281500 ms  DeleteObjects: 4.288800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.- Loaded All Assemblies, in  0.611 seconds
Refreshing native plugins compatible for Editor in 1.56 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  0.637 seconds
Domain Reload Profiling: 1248ms
	BeginReloadAssembly (163ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (38ms)
	RebuildCommonClasses (30ms)
	RebuildNativeTypeToScriptingClass (14ms)
	initialDomainReloadingComplete (23ms)
	LoadAllAssembliesAndSetupDomain (380ms)
		LoadAssemblies (296ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (164ms)
			TypeCache.Refresh (9ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (141ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (637ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (483ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (9ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (96ms)
			ProcessInitializeOnLoadAttributes (323ms)
			ProcessInitializeOnLoadMethodAttributes (47ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 2.73 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 33 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6551 unused Assets / (7.4 MB). Loaded Objects now: 7243.
Memory consumption went from 161.1 MB to 153.7 MB.
Total: 14.666300 ms (FindLiveObjects: 0.813900 ms CreateObjectMapping: 1.016700 ms MarkObjects: 6.782800 ms  DeleteObjects: 6.048700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.- Loaded All Assemblies, in  0.601 seconds
Refreshing native plugins compatible for Editor in 1.69 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  0.602 seconds
Domain Reload Profiling: 1203ms
	BeginReloadAssembly (161ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (38ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (24ms)
	LoadAllAssembliesAndSetupDomain (379ms)
		LoadAssemblies (301ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (157ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (137ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (602ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (446ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (10ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (84ms)
			ProcessInitializeOnLoadAttributes (287ms)
			ProcessInitializeOnLoadMethodAttributes (57ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 3.15 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 33 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6551 unused Assets / (7.3 MB). Loaded Objects now: 7245.
Memory consumption went from 161.1 MB to 153.8 MB.
Total: 13.088500 ms (FindLiveObjects: 0.757800 ms CreateObjectMapping: 0.866200 ms MarkObjects: 6.301000 ms  DeleteObjects: 5.161900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.- Loaded All Assemblies, in  0.620 seconds
Refreshing native plugins compatible for Editor in 1.62 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  0.758 seconds
Domain Reload Profiling: 1378ms
	BeginReloadAssembly (164ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (40ms)
	RebuildCommonClasses (30ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (23ms)
	LoadAllAssembliesAndSetupDomain (394ms)
		LoadAssemblies (302ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (170ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (150ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (758ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (596ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (10ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (98ms)
			ProcessInitializeOnLoadAttributes (314ms)
			ProcessInitializeOnLoadMethodAttributes (166ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 3.17 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 42 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6553 unused Assets / (7.2 MB). Loaded Objects now: 7255.
Memory consumption went from 162.7 MB to 155.5 MB.
Total: 14.526000 ms (FindLiveObjects: 0.784300 ms CreateObjectMapping: 0.895500 ms MarkObjects: 7.069300 ms  DeleteObjects: 5.775600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 3.94 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 35 Unused Serialized files (Serialized files now loaded: 0)
Unloading 33 unused Assets / (3.1 MB). Loaded Objects now: 7257.
Memory consumption went from 157.6 MB to 154.5 MB.
Total: 11.047700 ms (FindLiveObjects: 0.466600 ms CreateObjectMapping: 0.428000 ms MarkObjects: 7.666200 ms  DeleteObjects: 2.484700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.- Loaded All Assemblies, in  0.594 seconds
Refreshing native plugins compatible for Editor in 1.50 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  0.600 seconds
Domain Reload Profiling: 1195ms
	BeginReloadAssembly (157ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (36ms)
	RebuildCommonClasses (31ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (376ms)
		LoadAssemblies (294ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (159ms)
			TypeCache.Refresh (9ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (135ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (600ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (445ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (10ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (88ms)
			ProcessInitializeOnLoadAttributes (289ms)
			ProcessInitializeOnLoadMethodAttributes (48ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Refreshing native plugins compatible for Editor in 3.45 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 33 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6551 unused Assets / (7.1 MB). Loaded Objects now: 7259.
Memory consumption went from 161.6 MB to 154.5 MB.
Total: 12.135800 ms (FindLiveObjects: 0.773500 ms CreateObjectMapping: 0.997400 ms MarkObjects: 5.927100 ms  DeleteObjects: 4.435400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.- Loaded All Assemblies, in  0.595 seconds
Refreshing native plugins compatible for Editor in 1.58 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  0.600 seconds
Domain Reload Profiling: 1196ms
	BeginReloadAssembly (159ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (38ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (23ms)
	LoadAllAssembliesAndSetupDomain (374ms)
		LoadAssemblies (291ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (157ms)
			TypeCache.Refresh (9ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (134ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (601ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (448ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (9ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (86ms)
			ProcessInitializeOnLoadAttributes (300ms)
			ProcessInitializeOnLoadMethodAttributes (44ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Refreshing native plugins compatible for Editor in 2.92 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 33 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6551 unused Assets / (7.3 MB). Loaded Objects now: 7261.
Memory consumption went from 161.5 MB to 154.2 MB.
Total: 12.791500 ms (FindLiveObjects: 0.755100 ms CreateObjectMapping: 1.111500 ms MarkObjects: 6.174200 ms  DeleteObjects: 4.748800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.- Loaded All Assemblies, in  0.592 seconds
Refreshing native plugins compatible for Editor in 1.51 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  0.597 seconds
Domain Reload Profiling: 1189ms
	BeginReloadAssembly (153ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (34ms)
	RebuildCommonClasses (30ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (377ms)
		LoadAssemblies (295ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (159ms)
			TypeCache.Refresh (9ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (136ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (597ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (444ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (9ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (86ms)
			ProcessInitializeOnLoadAttributes (286ms)
			ProcessInitializeOnLoadMethodAttributes (54ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 2.83 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 33 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6551 unused Assets / (7.0 MB). Loaded Objects now: 7263.
Memory consumption went from 161.6 MB to 154.6 MB.
Total: 12.254600 ms (FindLiveObjects: 0.768700 ms CreateObjectMapping: 0.848200 ms MarkObjects: 5.780700 ms  DeleteObjects: 4.853500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.- Loaded All Assemblies, in  0.578 seconds
Refreshing native plugins compatible for Editor in 1.54 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  0.591 seconds
Domain Reload Profiling: 1171ms
	BeginReloadAssembly (157ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (36ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (362ms)
		LoadAssemblies (283ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (157ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (137ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (592ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (440ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (10ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (88ms)
			ProcessInitializeOnLoadAttributes (282ms)
			ProcessInitializeOnLoadMethodAttributes (52ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 2.88 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 33 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6551 unused Assets / (7.2 MB). Loaded Objects now: 7265.
Memory consumption went from 161.6 MB to 154.4 MB.
Total: 12.820500 ms (FindLiveObjects: 0.704500 ms CreateObjectMapping: 0.792800 ms MarkObjects: 6.463800 ms  DeleteObjects: 4.858000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.- Loaded All Assemblies, in  0.576 seconds
Refreshing native plugins compatible for Editor in 1.73 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  0.626 seconds
Domain Reload Profiling: 1202ms
	BeginReloadAssembly (158ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (40ms)
	RebuildCommonClasses (27ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (21ms)
	LoadAllAssembliesAndSetupDomain (361ms)
		LoadAssemblies (279ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (157ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (136ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (626ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (464ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (10ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (95ms)
			ProcessInitializeOnLoadAttributes (301ms)
			ProcessInitializeOnLoadMethodAttributes (50ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 4.38 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 33 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6551 unused Assets / (7.2 MB). Loaded Objects now: 7267.
Memory consumption went from 161.6 MB to 154.4 MB.
Total: 12.777800 ms (FindLiveObjects: 0.795700 ms CreateObjectMapping: 0.861400 ms MarkObjects: 6.429800 ms  DeleteObjects: 4.687900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.- Loaded All Assemblies, in  0.600 seconds
Refreshing native plugins compatible for Editor in 1.79 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  0.596 seconds
Domain Reload Profiling: 1197ms
	BeginReloadAssembly (162ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (35ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (27ms)
	LoadAllAssembliesAndSetupDomain (374ms)
		LoadAssemblies (301ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (157ms)
			TypeCache.Refresh (9ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (135ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (596ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (446ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (9ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (88ms)
			ProcessInitializeOnLoadAttributes (302ms)
			ProcessInitializeOnLoadMethodAttributes (40ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 3.09 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 33 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6551 unused Assets / (7.2 MB). Loaded Objects now: 7269.
Memory consumption went from 161.6 MB to 154.4 MB.
Total: 12.921200 ms (FindLiveObjects: 0.784500 ms CreateObjectMapping: 1.010100 ms MarkObjects: 6.483100 ms  DeleteObjects: 4.641600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.- Loaded All Assemblies, in  0.606 seconds
Refreshing native plugins compatible for Editor in 1.48 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  0.605 seconds
Domain Reload Profiling: 1212ms
	BeginReloadAssembly (163ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (38ms)
	RebuildCommonClasses (31ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (23ms)
	LoadAllAssembliesAndSetupDomain (379ms)
		LoadAssemblies (301ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (160ms)
			TypeCache.Refresh (9ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (137ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (606ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (450ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (9ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (88ms)
			ProcessInitializeOnLoadAttributes (288ms)
			ProcessInitializeOnLoadMethodAttributes (56ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (13ms)
Refreshing native plugins compatible for Editor in 2.68 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 33 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6551 unused Assets / (7.3 MB). Loaded Objects now: 7271.
Memory consumption went from 161.5 MB to 154.3 MB.
Total: 11.660700 ms (FindLiveObjects: 0.702100 ms CreateObjectMapping: 0.823000 ms MarkObjects: 5.754300 ms  DeleteObjects: 4.379000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.- Loaded All Assemblies, in  0.571 seconds
Refreshing native plugins compatible for Editor in 1.48 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  0.596 seconds
Domain Reload Profiling: 1167ms
	BeginReloadAssembly (149ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (33ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (362ms)
		LoadAssemblies (282ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (155ms)
			TypeCache.Refresh (8ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (134ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (596ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (441ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (9ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (95ms)
			ProcessInitializeOnLoadAttributes (284ms)
			ProcessInitializeOnLoadMethodAttributes (46ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 2.83 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 33 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6551 unused Assets / (7.2 MB). Loaded Objects now: 7273.
Memory consumption went from 161.6 MB to 154.4 MB.
Total: 12.529800 ms (FindLiveObjects: 0.916000 ms CreateObjectMapping: 0.994500 ms MarkObjects: 6.178500 ms  DeleteObjects: 4.438100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 2.82 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 33 Unused Serialized files (Serialized files now loaded: 0)
Unloading 33 unused Assets / (3.1 MB). Loaded Objects now: 7273.
Memory consumption went from 157.6 MB to 154.5 MB.
Total: 7.490300 ms (FindLiveObjects: 0.346200 ms CreateObjectMapping: 0.303000 ms MarkObjects: 5.593500 ms  DeleteObjects: 1.246200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.- Loaded All Assemblies, in  0.922 seconds
Refreshing native plugins compatible for Editor in 2.53 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  0.652 seconds
Domain Reload Profiling: 1573ms
	BeginReloadAssembly (279ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (12ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (61ms)
	RebuildCommonClasses (42ms)
	RebuildNativeTypeToScriptingClass (17ms)
	initialDomainReloadingComplete (37ms)
	LoadAllAssembliesAndSetupDomain (545ms)
		LoadAssemblies (503ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (180ms)
			TypeCache.Refresh (8ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (154ms)
			ResolveRequiredComponents (13ms)
	FinalizeReload (653ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (467ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (10ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (97ms)
			ProcessInitializeOnLoadAttributes (306ms)
			ProcessInitializeOnLoadMethodAttributes (45ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 2.70 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 33 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6551 unused Assets / (7.3 MB). Loaded Objects now: 7275.
Memory consumption went from 161.6 MB to 154.3 MB.
Total: 12.511800 ms (FindLiveObjects: 0.699300 ms CreateObjectMapping: 0.925900 ms MarkObjects: 5.923300 ms  DeleteObjects: 4.961200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.- Loaded All Assemblies, in  1.529 seconds
Refreshing native plugins compatible for Editor in 4.04 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  1.492 seconds
Domain Reload Profiling: 3016ms
	BeginReloadAssembly (455ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (15ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (141ms)
	RebuildCommonClasses (74ms)
	RebuildNativeTypeToScriptingClass (29ms)
	initialDomainReloadingComplete (62ms)
	LoadAllAssembliesAndSetupDomain (903ms)
		LoadAssemblies (752ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (351ms)
			TypeCache.Refresh (17ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (302ms)
			ResolveRequiredComponents (23ms)
	FinalizeReload (1493ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1037ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (19ms)
			SetLoadedEditorAssemblies (8ms)
			BeforeProcessingInitializeOnLoad (202ms)
			ProcessInitializeOnLoadAttributes (680ms)
			ProcessInitializeOnLoadMethodAttributes (116ms)
			AfterProcessingInitializeOnLoad (12ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (33ms)
Refreshing native plugins compatible for Editor in 4.65 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 33 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6551 unused Assets / (7.5 MB). Loaded Objects now: 7277.
Memory consumption went from 161.6 MB to 154.1 MB.
Total: 15.937600 ms (FindLiveObjects: 1.096100 ms CreateObjectMapping: 0.815400 ms MarkObjects: 8.393600 ms  DeleteObjects: 5.631000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.030 seconds
Refreshing native plugins compatible for Editor in 1.60 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  0.751 seconds
Domain Reload Profiling: 1783ms
	BeginReloadAssembly (283ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (17ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (87ms)
	RebuildCommonClasses (34ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (29ms)
	LoadAllAssembliesAndSetupDomain (674ms)
		LoadAssemblies (515ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (269ms)
			TypeCache.Refresh (141ms)
				TypeCache.ScanAssembly (112ms)
			BuildScriptInfoCaches (112ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (752ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (570ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (12ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (132ms)
			ProcessInitializeOnLoadAttributes (354ms)
			ProcessInitializeOnLoadMethodAttributes (60ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (13ms)
Refreshing native plugins compatible for Editor in 3.45 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 33 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6553 unused Assets / (7.3 MB). Loaded Objects now: 7279.
Memory consumption went from 161.8 MB to 154.5 MB.
Total: 16.836800 ms (FindLiveObjects: 0.794300 ms CreateObjectMapping: 0.925400 ms MarkObjects: 8.947000 ms  DeleteObjects: 6.167000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.043 seconds
Refreshing native plugins compatible for Editor in 1.75 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mirror | mirror-networking.com | discord.gg/N9QVxbM
Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  0.776 seconds
Domain Reload Profiling: 1820ms
	BeginReloadAssembly (291ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (14ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (120ms)
	RebuildCommonClasses (34ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (30ms)
	LoadAllAssembliesAndSetupDomain (677ms)
		LoadAssemblies (466ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (299ms)
			TypeCache.Refresh (164ms)
				TypeCache.ScanAssembly (149ms)
			BuildScriptInfoCaches (116ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (776ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (588ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (11ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (114ms)
			ProcessInitializeOnLoadAttributes (392ms)
			ProcessInitializeOnLoadMethodAttributes (64ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Refreshing native plugins compatible for Editor in 3.25 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 37 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7128 unused Assets / (8.5 MB). Loaded Objects now: 7846.
Memory consumption went from 168.1 MB to 159.5 MB.
Total: 15.198000 ms (FindLiveObjects: 0.785000 ms CreateObjectMapping: 0.948600 ms MarkObjects: 6.982900 ms  DeleteObjects: 6.479500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.956 seconds
Refreshing native plugins compatible for Editor in 1.79 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation. ---> System.NullReferenceException: Object reference not set to an instance of an object
  at Unity.Multiplayer.Playmode.Workflow.Editor.ProjectDataStore..ctor (System.String directory) [0x0006e] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Configuration\ProjectDataStore.cs:55 
  at Unity.Multiplayer.Playmode.Workflow.Editor.ProjectDataStore.GetMain () [0x00000] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Configuration\ProjectDataStore.cs:36 
  at Unity.Multiplayer.Playmode.Workflow.Editor.WorkflowMainEditorContext..ctor (Unity.Multiplayer.Playmode.VirtualProjects.Editor.MainEditorContext mainEditorContext) [0x0002c] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Contexts\WorkflowMainEditorContext.cs:15 
  at Unity.Multiplayer.Playmode.Workflow.Editor.VirtualProjectWorkflow.InitializeMPPMContexts () [0x00018] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Workflow\VirtualProjectWorkflow.cs:198 
  at Unity.Multiplayer.Playmode.Workflow.Editor.VirtualProjectWorkflow+<>c.<.cctor>b__22_0 () [0x00036] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Workflow\VirtualProjectWorkflow.cs:85 
  at Unity.Multiplayer.Playmode.VirtualProjects.Editor.EditorContexts.SendReadyEvent () [0x00038] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\VirtualProjects\Editor\Contexts\EditorContexts.cs:56 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <********************************>:0 
   --- End of inner exception stack trace ---
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x00083] in <********************************>:0 
  at System.Reflection.MethodBase.Invoke (System.Object obj, System.Object[] parameters) [0x00000] in <********************************>:0 
  at UnityEditor.EditorAssemblies.ProcessInitializeOnLoadMethodAttributes () [0x00056] in <f8e1f14ef858402482f58cef2e987b5b>:0 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  0.926 seconds
Domain Reload Profiling: 1882ms
	BeginReloadAssembly (195ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (50ms)
	RebuildCommonClasses (35ms)
	RebuildNativeTypeToScriptingClass (14ms)
	initialDomainReloadingComplete (31ms)
	LoadAllAssembliesAndSetupDomain (680ms)
		LoadAssemblies (527ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (246ms)
			TypeCache.Refresh (17ms)
				TypeCache.ScanAssembly (5ms)
			BuildScriptInfoCaches (209ms)
			ResolveRequiredComponents (16ms)
	FinalizeReload (926ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (736ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (10ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (133ms)
			ProcessInitializeOnLoadAttributes (369ms)
			ProcessInitializeOnLoadMethodAttributes (216ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (13ms)
Refreshing native plugins compatible for Editor in 4.19 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 37 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7342 unused Assets / (8.6 MB). Loaded Objects now: 8064.
Memory consumption went from 169.4 MB to 160.8 MB.
Total: 16.855700 ms (FindLiveObjects: 0.879000 ms CreateObjectMapping: 1.097200 ms MarkObjects: 7.698700 ms  DeleteObjects: 7.178500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.211 seconds
Refreshing native plugins compatible for Editor in 3.81 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation. ---> System.NullReferenceException: Object reference not set to an instance of an object
  at Unity.Multiplayer.Playmode.Workflow.Editor.ProjectDataStore..ctor (System.String directory) [0x0006e] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Configuration\ProjectDataStore.cs:55 
  at Unity.Multiplayer.Playmode.Workflow.Editor.ProjectDataStore.GetMain () [0x00000] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Configuration\ProjectDataStore.cs:36 
  at Unity.Multiplayer.Playmode.Workflow.Editor.WorkflowMainEditorContext..ctor (Unity.Multiplayer.Playmode.VirtualProjects.Editor.MainEditorContext mainEditorContext) [0x0002c] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Contexts\WorkflowMainEditorContext.cs:15 
  at Unity.Multiplayer.Playmode.Workflow.Editor.VirtualProjectWorkflow.InitializeMPPMContexts () [0x00018] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Workflow\VirtualProjectWorkflow.cs:198 
  at Unity.Multiplayer.Playmode.Workflow.Editor.VirtualProjectWorkflow+<>c.<.cctor>b__22_0 () [0x00036] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Workflow\VirtualProjectWorkflow.cs:85 
  at Unity.Multiplayer.Playmode.VirtualProjects.Editor.EditorContexts.SendReadyEvent () [0x00038] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\VirtualProjects\Editor\Contexts\EditorContexts.cs:56 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <********************************>:0 
   --- End of inner exception stack trace ---
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x00083] in <********************************>:0 
  at System.Reflection.MethodBase.Invoke (System.Object obj, System.Object[] parameters) [0x00000] in <********************************>:0 
  at UnityEditor.EditorAssemblies.ProcessInitializeOnLoadMethodAttributes () [0x00056] in <f8e1f14ef858402482f58cef2e987b5b>:0 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  1.050 seconds
Domain Reload Profiling: 2263ms
	BeginReloadAssembly (266ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (9ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (89ms)
	RebuildCommonClasses (38ms)
	RebuildNativeTypeToScriptingClass (13ms)
	initialDomainReloadingComplete (33ms)
	LoadAllAssembliesAndSetupDomain (863ms)
		LoadAssemblies (706ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (264ms)
			TypeCache.Refresh (10ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (230ms)
			ResolveRequiredComponents (18ms)
	FinalizeReload (1050ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (826ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (12ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (158ms)
			ProcessInitializeOnLoadAttributes (432ms)
			ProcessInitializeOnLoadMethodAttributes (215ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (14ms)
Refreshing native plugins compatible for Editor in 5.23 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 37 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7342 unused Assets / (8.6 MB). Loaded Objects now: 8066.
Memory consumption went from 168.8 MB to 160.2 MB.
Total: 18.822400 ms (FindLiveObjects: 0.935900 ms CreateObjectMapping: 1.226300 ms MarkObjects: 8.382800 ms  DeleteObjects: 8.274900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 42.39 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 37 Unused Serialized files (Serialized files now loaded: 0)
Unloading 37 unused Assets / (3.7 MB). Loaded Objects now: 8066.
Memory consumption went from 163.5 MB to 159.9 MB.
Total: 206.605200 ms (FindLiveObjects: 2.353200 ms CreateObjectMapping: 0.951400 ms MarkObjects: 196.961300 ms  DeleteObjects: 6.323900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.335 seconds
Refreshing native plugins compatible for Editor in 1.72 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation. ---> System.NullReferenceException: Object reference not set to an instance of an object
  at Unity.Multiplayer.Playmode.Workflow.Editor.ProjectDataStore..ctor (System.String directory) [0x0006e] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Configuration\ProjectDataStore.cs:55 
  at Unity.Multiplayer.Playmode.Workflow.Editor.ProjectDataStore.GetMain () [0x00000] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Configuration\ProjectDataStore.cs:36 
  at Unity.Multiplayer.Playmode.Workflow.Editor.WorkflowMainEditorContext..ctor (Unity.Multiplayer.Playmode.VirtualProjects.Editor.MainEditorContext mainEditorContext) [0x0002c] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Contexts\WorkflowMainEditorContext.cs:15 
  at Unity.Multiplayer.Playmode.Workflow.Editor.VirtualProjectWorkflow.InitializeMPPMContexts () [0x00018] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Workflow\VirtualProjectWorkflow.cs:198 
  at Unity.Multiplayer.Playmode.Workflow.Editor.VirtualProjectWorkflow+<>c.<.cctor>b__22_0 () [0x00036] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Workflow\VirtualProjectWorkflow.cs:85 
  at Unity.Multiplayer.Playmode.VirtualProjects.Editor.EditorContexts.SendReadyEvent () [0x00038] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\VirtualProjects\Editor\Contexts\EditorContexts.cs:56 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <********************************>:0 
   --- End of inner exception stack trace ---
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x00083] in <********************************>:0 
  at System.Reflection.MethodBase.Invoke (System.Object obj, System.Object[] parameters) [0x00000] in <********************************>:0 
  at UnityEditor.EditorAssemblies.ProcessInitializeOnLoadMethodAttributes () [0x00056] in <f8e1f14ef858402482f58cef2e987b5b>:0 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  0.975 seconds
Domain Reload Profiling: 2311ms
	BeginReloadAssembly (365ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (24ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (122ms)
	RebuildCommonClasses (45ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (34ms)
	LoadAllAssembliesAndSetupDomain (882ms)
		LoadAssemblies (736ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (283ms)
			TypeCache.Refresh (20ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (230ms)
			ResolveRequiredComponents (27ms)
	FinalizeReload (975ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (711ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (17ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (143ms)
			ProcessInitializeOnLoadAttributes (372ms)
			ProcessInitializeOnLoadMethodAttributes (168ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (16ms)
Refreshing native plugins compatible for Editor in 3.98 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 37 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7342 unused Assets / (9.4 MB). Loaded Objects now: 8068.
Memory consumption went from 168.8 MB to 159.4 MB.
Total: 18.338100 ms (FindLiveObjects: 0.835600 ms CreateObjectMapping: 1.003100 ms MarkObjects: 8.072700 ms  DeleteObjects: 8.424600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.858 seconds
Refreshing native plugins compatible for Editor in 2.03 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation. ---> System.NullReferenceException: Object reference not set to an instance of an object
  at Unity.Multiplayer.Playmode.Workflow.Editor.ProjectDataStore..ctor (System.String directory) [0x0006e] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Configuration\ProjectDataStore.cs:55 
  at Unity.Multiplayer.Playmode.Workflow.Editor.ProjectDataStore.GetMain () [0x00000] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Configuration\ProjectDataStore.cs:36 
  at Unity.Multiplayer.Playmode.Workflow.Editor.WorkflowMainEditorContext..ctor (Unity.Multiplayer.Playmode.VirtualProjects.Editor.MainEditorContext mainEditorContext) [0x0002c] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Contexts\WorkflowMainEditorContext.cs:15 
  at Unity.Multiplayer.Playmode.Workflow.Editor.VirtualProjectWorkflow.InitializeMPPMContexts () [0x00018] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Workflow\VirtualProjectWorkflow.cs:198 
  at Unity.Multiplayer.Playmode.Workflow.Editor.VirtualProjectWorkflow+<>c.<.cctor>b__22_0 () [0x00036] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Workflow\VirtualProjectWorkflow.cs:85 
  at Unity.Multiplayer.Playmode.VirtualProjects.Editor.EditorContexts.SendReadyEvent () [0x00038] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\VirtualProjects\Editor\Contexts\EditorContexts.cs:56 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <********************************>:0 
   --- End of inner exception stack trace ---
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x00083] in <********************************>:0 
  at System.Reflection.MethodBase.Invoke (System.Object obj, System.Object[] parameters) [0x00000] in <********************************>:0 
  at UnityEditor.EditorAssemblies.ProcessInitializeOnLoadMethodAttributes () [0x00056] in <f8e1f14ef858402482f58cef2e987b5b>:0 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  0.864 seconds
Domain Reload Profiling: 1722ms
	BeginReloadAssembly (207ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (55ms)
	RebuildCommonClasses (37ms)
	RebuildNativeTypeToScriptingClass (13ms)
	initialDomainReloadingComplete (31ms)
	LoadAllAssembliesAndSetupDomain (571ms)
		LoadAssemblies (404ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (266ms)
			TypeCache.Refresh (9ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (236ms)
			ResolveRequiredComponents (15ms)
	FinalizeReload (864ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (654ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (11ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (134ms)
			ProcessInitializeOnLoadAttributes (334ms)
			ProcessInitializeOnLoadMethodAttributes (165ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Refreshing native plugins compatible for Editor in 4.53 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 37 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7342 unused Assets / (8.7 MB). Loaded Objects now: 8070.
Memory consumption went from 168.8 MB to 160.1 MB.
Total: 19.786700 ms (FindLiveObjects: 1.203300 ms CreateObjectMapping: 1.490000 ms MarkObjects: 9.961700 ms  DeleteObjects: 7.128700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 3.43 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 37 Unused Serialized files (Serialized files now loaded: 0)
Unloading 37 unused Assets / (3.7 MB). Loaded Objects now: 8070.
Memory consumption went from 163.6 MB to 159.9 MB.
Total: 10.391800 ms (FindLiveObjects: 0.431800 ms CreateObjectMapping: 0.394100 ms MarkObjects: 8.002600 ms  DeleteObjects: 1.562100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  3.198 seconds
Refreshing native plugins compatible for Editor in 15.52 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation. ---> System.NullReferenceException: Object reference not set to an instance of an object
  at Unity.Multiplayer.Playmode.Workflow.Editor.ProjectDataStore..ctor (System.String directory) [0x0006e] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Configuration\ProjectDataStore.cs:55 
  at Unity.Multiplayer.Playmode.Workflow.Editor.ProjectDataStore.GetMain () [0x00000] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Configuration\ProjectDataStore.cs:36 
  at Unity.Multiplayer.Playmode.Workflow.Editor.WorkflowMainEditorContext..ctor (Unity.Multiplayer.Playmode.VirtualProjects.Editor.MainEditorContext mainEditorContext) [0x0002c] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Contexts\WorkflowMainEditorContext.cs:15 
  at Unity.Multiplayer.Playmode.Workflow.Editor.VirtualProjectWorkflow.InitializeMPPMContexts () [0x00018] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Workflow\VirtualProjectWorkflow.cs:198 
  at Unity.Multiplayer.Playmode.Workflow.Editor.VirtualProjectWorkflow+<>c.<.cctor>b__22_0 () [0x00036] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Workflow\VirtualProjectWorkflow.cs:85 
  at Unity.Multiplayer.Playmode.VirtualProjects.Editor.EditorContexts.SendReadyEvent () [0x00038] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\VirtualProjects\Editor\Contexts\EditorContexts.cs:56 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <********************************>:0 
   --- End of inner exception stack trace ---
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x00083] in <********************************>:0 
  at System.Reflection.MethodBase.Invoke (System.Object obj, System.Object[] parameters) [0x00000] in <********************************>:0 
  at UnityEditor.EditorAssemblies.ProcessInitializeOnLoadMethodAttributes () [0x00056] in <f8e1f14ef858402482f58cef2e987b5b>:0 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  3.534 seconds
Domain Reload Profiling: 6718ms
	BeginReloadAssembly (653ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (31ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (144ms)
	RebuildCommonClasses (123ms)
	RebuildNativeTypeToScriptingClass (51ms)
	initialDomainReloadingComplete (127ms)
	LoadAllAssembliesAndSetupDomain (2228ms)
		LoadAssemblies (1619ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (939ms)
			TypeCache.Refresh (38ms)
				TypeCache.ScanAssembly (3ms)
			BuildScriptInfoCaches (820ms)
			ResolveRequiredComponents (63ms)
	FinalizeReload (3536ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (2758ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (39ms)
			SetLoadedEditorAssemblies (23ms)
			BeforeProcessingInitializeOnLoad (473ms)
			ProcessInitializeOnLoadAttributes (1444ms)
			ProcessInitializeOnLoadMethodAttributes (756ms)
			AfterProcessingInitializeOnLoad (21ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (57ms)
Refreshing native plugins compatible for Editor in 11.30 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 37 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7342 unused Assets / (9.8 MB). Loaded Objects now: 8072.
Memory consumption went from 168.8 MB to 159.0 MB.
Total: 34.932600 ms (FindLiveObjects: 2.283000 ms CreateObjectMapping: 1.935300 ms MarkObjects: 15.096100 ms  DeleteObjects: 15.615100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.760 seconds
Refreshing native plugins compatible for Editor in 2.22 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation. ---> System.NullReferenceException: Object reference not set to an instance of an object
  at Unity.Multiplayer.Playmode.Workflow.Editor.ProjectDataStore..ctor (System.String directory) [0x0006e] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Configuration\ProjectDataStore.cs:55 
  at Unity.Multiplayer.Playmode.Workflow.Editor.ProjectDataStore.GetMain () [0x00000] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Configuration\ProjectDataStore.cs:36 
  at Unity.Multiplayer.Playmode.Workflow.Editor.WorkflowMainEditorContext..ctor (Unity.Multiplayer.Playmode.VirtualProjects.Editor.MainEditorContext mainEditorContext) [0x0002c] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Contexts\WorkflowMainEditorContext.cs:15 
  at Unity.Multiplayer.Playmode.Workflow.Editor.VirtualProjectWorkflow.InitializeMPPMContexts () [0x00018] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Workflow\VirtualProjectWorkflow.cs:198 
  at Unity.Multiplayer.Playmode.Workflow.Editor.VirtualProjectWorkflow+<>c.<.cctor>b__22_0 () [0x00036] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Workflow\VirtualProjectWorkflow.cs:85 
  at Unity.Multiplayer.Playmode.VirtualProjects.Editor.EditorContexts.SendReadyEvent () [0x00038] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\VirtualProjects\Editor\Contexts\EditorContexts.cs:56 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <********************************>:0 
   --- End of inner exception stack trace ---
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x00083] in <********************************>:0 
  at System.Reflection.MethodBase.Invoke (System.Object obj, System.Object[] parameters) [0x00000] in <********************************>:0 
  at UnityEditor.EditorAssemblies.ProcessInitializeOnLoadMethodAttributes () [0x00056] in <f8e1f14ef858402482f58cef2e987b5b>:0 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  0.877 seconds
Domain Reload Profiling: 1638ms
	BeginReloadAssembly (177ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (50ms)
	RebuildCommonClasses (32ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (27ms)
	LoadAllAssembliesAndSetupDomain (513ms)
		LoadAssemblies (366ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (225ms)
			TypeCache.Refresh (12ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (196ms)
			ResolveRequiredComponents (13ms)
	FinalizeReload (877ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (697ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (11ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (170ms)
			ProcessInitializeOnLoadAttributes (321ms)
			ProcessInitializeOnLoadMethodAttributes (187ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Refreshing native plugins compatible for Editor in 3.80 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 37 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7342 unused Assets / (8.4 MB). Loaded Objects now: 8074.
Memory consumption went from 168.8 MB to 160.5 MB.
Total: 16.102900 ms (FindLiveObjects: 0.854800 ms CreateObjectMapping: 0.979800 ms MarkObjects: 7.833400 ms  DeleteObjects: 6.433200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.768 seconds
Refreshing native plugins compatible for Editor in 2.39 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation. ---> System.NullReferenceException: Object reference not set to an instance of an object
  at Unity.Multiplayer.Playmode.Workflow.Editor.ProjectDataStore..ctor (System.String directory) [0x0006e] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Configuration\ProjectDataStore.cs:55 
  at Unity.Multiplayer.Playmode.Workflow.Editor.ProjectDataStore.GetMain () [0x00000] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Configuration\ProjectDataStore.cs:36 
  at Unity.Multiplayer.Playmode.Workflow.Editor.WorkflowMainEditorContext..ctor (Unity.Multiplayer.Playmode.VirtualProjects.Editor.MainEditorContext mainEditorContext) [0x0002c] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Contexts\WorkflowMainEditorContext.cs:15 
  at Unity.Multiplayer.Playmode.Workflow.Editor.VirtualProjectWorkflow.InitializeMPPMContexts () [0x00018] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Workflow\VirtualProjectWorkflow.cs:198 
  at Unity.Multiplayer.Playmode.Workflow.Editor.VirtualProjectWorkflow+<>c.<.cctor>b__22_0 () [0x00036] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Workflow\VirtualProjectWorkflow.cs:85 
  at Unity.Multiplayer.Playmode.VirtualProjects.Editor.EditorContexts.SendReadyEvent () [0x00038] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\VirtualProjects\Editor\Contexts\EditorContexts.cs:56 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <********************************>:0 
   --- End of inner exception stack trace ---
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x00083] in <********************************>:0 
  at System.Reflection.MethodBase.Invoke (System.Object obj, System.Object[] parameters) [0x00000] in <********************************>:0 
  at UnityEditor.EditorAssemblies.ProcessInitializeOnLoadMethodAttributes () [0x00056] in <f8e1f14ef858402482f58cef2e987b5b>:0 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  0.803 seconds
Domain Reload Profiling: 1572ms
	BeginReloadAssembly (213ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (51ms)
	RebuildCommonClasses (32ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (26ms)
	LoadAllAssembliesAndSetupDomain (487ms)
		LoadAssemblies (350ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (225ms)
			TypeCache.Refresh (8ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (199ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (804ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (614ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (10ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (120ms)
			ProcessInitializeOnLoadAttributes (305ms)
			ProcessInitializeOnLoadMethodAttributes (171ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 4.39 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 37 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7342 unused Assets / (7.7 MB). Loaded Objects now: 8076.
Memory consumption went from 168.8 MB to 161.2 MB.
Total: 16.503300 ms (FindLiveObjects: 0.879400 ms CreateObjectMapping: 1.133300 ms MarkObjects: 7.732700 ms  DeleteObjects: 6.756100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 19.62 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 37 Unused Serialized files (Serialized files now loaded: 0)
Unloading 37 unused Assets / (3.7 MB). Loaded Objects now: 8076.
Memory consumption went from 163.6 MB to 159.9 MB.
Total: 13.849700 ms (FindLiveObjects: 0.535500 ms CreateObjectMapping: 0.469800 ms MarkObjects: 10.346000 ms  DeleteObjects: 2.495100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.641 seconds
Refreshing native plugins compatible for Editor in 1.79 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation. ---> System.NullReferenceException: Object reference not set to an instance of an object
  at Unity.Multiplayer.Playmode.Workflow.Editor.ProjectDataStore..ctor (System.String directory) [0x0006e] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Configuration\ProjectDataStore.cs:55 
  at Unity.Multiplayer.Playmode.Workflow.Editor.ProjectDataStore.GetMain () [0x00000] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Configuration\ProjectDataStore.cs:36 
  at Unity.Multiplayer.Playmode.Workflow.Editor.WorkflowMainEditorContext..ctor (Unity.Multiplayer.Playmode.VirtualProjects.Editor.MainEditorContext mainEditorContext) [0x0002c] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Contexts\WorkflowMainEditorContext.cs:15 
  at Unity.Multiplayer.Playmode.Workflow.Editor.VirtualProjectWorkflow.InitializeMPPMContexts () [0x00018] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Workflow\VirtualProjectWorkflow.cs:198 
  at Unity.Multiplayer.Playmode.Workflow.Editor.VirtualProjectWorkflow+<>c.<.cctor>b__22_0 () [0x00036] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Workflow\VirtualProjectWorkflow.cs:85 
  at Unity.Multiplayer.Playmode.VirtualProjects.Editor.EditorContexts.SendReadyEvent () [0x00038] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\VirtualProjects\Editor\Contexts\EditorContexts.cs:56 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <********************************>:0 
   --- End of inner exception stack trace ---
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x00083] in <********************************>:0 
  at System.Reflection.MethodBase.Invoke (System.Object obj, System.Object[] parameters) [0x00000] in <********************************>:0 
  at UnityEditor.EditorAssemblies.ProcessInitializeOnLoadMethodAttributes () [0x00056] in <f8e1f14ef858402482f58cef2e987b5b>:0 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  1.120 seconds
Domain Reload Profiling: 2764ms
	BeginReloadAssembly (559ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (62ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (203ms)
	RebuildCommonClasses (36ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (34ms)
	LoadAllAssembliesAndSetupDomain (1003ms)
		LoadAssemblies (866ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (288ms)
			TypeCache.Refresh (18ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (242ms)
			ResolveRequiredComponents (20ms)
	FinalizeReload (1121ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (887ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (13ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (122ms)
			ProcessInitializeOnLoadAttributes (478ms)
			ProcessInitializeOnLoadMethodAttributes (265ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (17ms)
Refreshing native plugins compatible for Editor in 4.00 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 37 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7342 unused Assets / (9.1 MB). Loaded Objects now: 8078.
Memory consumption went from 168.9 MB to 159.8 MB.
Total: 18.539800 ms (FindLiveObjects: 2.070700 ms CreateObjectMapping: 0.932900 ms MarkObjects: 7.933700 ms  DeleteObjects: 7.599700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.805 seconds
Refreshing native plugins compatible for Editor in 1.99 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation. ---> System.NullReferenceException: Object reference not set to an instance of an object
  at Unity.Multiplayer.Playmode.Workflow.Editor.ProjectDataStore..ctor (System.String directory) [0x0006e] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Configuration\ProjectDataStore.cs:55 
  at Unity.Multiplayer.Playmode.Workflow.Editor.ProjectDataStore.GetMain () [0x00000] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Configuration\ProjectDataStore.cs:36 
  at Unity.Multiplayer.Playmode.Workflow.Editor.WorkflowMainEditorContext..ctor (Unity.Multiplayer.Playmode.VirtualProjects.Editor.MainEditorContext mainEditorContext) [0x0002c] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Contexts\WorkflowMainEditorContext.cs:15 
  at Unity.Multiplayer.Playmode.Workflow.Editor.VirtualProjectWorkflow.InitializeMPPMContexts () [0x00018] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Workflow\VirtualProjectWorkflow.cs:198 
  at Unity.Multiplayer.Playmode.Workflow.Editor.VirtualProjectWorkflow+<>c.<.cctor>b__22_0 () [0x00036] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Workflow\VirtualProjectWorkflow.cs:85 
  at Unity.Multiplayer.Playmode.VirtualProjects.Editor.EditorContexts.SendReadyEvent () [0x00038] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\VirtualProjects\Editor\Contexts\EditorContexts.cs:56 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <********************************>:0 
   --- End of inner exception stack trace ---
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x00083] in <********************************>:0 
  at System.Reflection.MethodBase.Invoke (System.Object obj, System.Object[] parameters) [0x00000] in <********************************>:0 
  at UnityEditor.EditorAssemblies.ProcessInitializeOnLoadMethodAttributes () [0x00056] in <f8e1f14ef858402482f58cef2e987b5b>:0 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  0.776 seconds
Domain Reload Profiling: 1582ms
	BeginReloadAssembly (199ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (9ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (48ms)
	RebuildCommonClasses (38ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (27ms)
	LoadAllAssembliesAndSetupDomain (531ms)
		LoadAssemblies (406ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (216ms)
			TypeCache.Refresh (12ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (187ms)
			ResolveRequiredComponents (13ms)
	FinalizeReload (777ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (603ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (9ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (99ms)
			ProcessInitializeOnLoadAttributes (324ms)
			ProcessInitializeOnLoadMethodAttributes (164ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 4.04 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 37 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7342 unused Assets / (8.9 MB). Loaded Objects now: 8080.
Memory consumption went from 168.9 MB to 160.0 MB.
Total: 16.456600 ms (FindLiveObjects: 0.872800 ms CreateObjectMapping: 0.876500 ms MarkObjects: 7.775300 ms  DeleteObjects: 6.929500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.689 seconds
Refreshing native plugins compatible for Editor in 1.63 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation. ---> System.NullReferenceException: Object reference not set to an instance of an object
  at Unity.Multiplayer.Playmode.Workflow.Editor.ProjectDataStore..ctor (System.String directory) [0x0006e] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Configuration\ProjectDataStore.cs:55 
  at Unity.Multiplayer.Playmode.Workflow.Editor.ProjectDataStore.GetMain () [0x00000] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Configuration\ProjectDataStore.cs:36 
  at Unity.Multiplayer.Playmode.Workflow.Editor.WorkflowMainEditorContext..ctor (Unity.Multiplayer.Playmode.VirtualProjects.Editor.MainEditorContext mainEditorContext) [0x0002c] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Contexts\WorkflowMainEditorContext.cs:15 
  at Unity.Multiplayer.Playmode.Workflow.Editor.VirtualProjectWorkflow.InitializeMPPMContexts () [0x00018] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Workflow\VirtualProjectWorkflow.cs:198 
  at Unity.Multiplayer.Playmode.Workflow.Editor.VirtualProjectWorkflow+<>c.<.cctor>b__22_0 () [0x00036] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Workflow\VirtualProjectWorkflow.cs:85 
  at Unity.Multiplayer.Playmode.VirtualProjects.Editor.EditorContexts.SendReadyEvent () [0x00038] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\VirtualProjects\Editor\Contexts\EditorContexts.cs:56 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <********************************>:0 
   --- End of inner exception stack trace ---
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x00083] in <********************************>:0 
  at System.Reflection.MethodBase.Invoke (System.Object obj, System.Object[] parameters) [0x00000] in <********************************>:0 
  at UnityEditor.EditorAssemblies.ProcessInitializeOnLoadMethodAttributes () [0x00056] in <f8e1f14ef858402482f58cef2e987b5b>:0 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  0.761 seconds
Domain Reload Profiling: 1452ms
	BeginReloadAssembly (168ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (46ms)
	RebuildCommonClasses (31ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (24ms)
	LoadAllAssembliesAndSetupDomain (454ms)
		LoadAssemblies (328ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (206ms)
			TypeCache.Refresh (8ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (180ms)
			ResolveRequiredComponents (13ms)
	FinalizeReload (762ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (574ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (11ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (94ms)
			ProcessInitializeOnLoadAttributes (313ms)
			ProcessInitializeOnLoadMethodAttributes (149ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 2.83 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 37 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7342 unused Assets / (9.2 MB). Loaded Objects now: 8082.
Memory consumption went from 168.9 MB to 159.7 MB.
Total: 16.477900 ms (FindLiveObjects: 1.154700 ms CreateObjectMapping: 0.909400 ms MarkObjects: 7.218900 ms  DeleteObjects: 7.193000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.686 seconds
Refreshing native plugins compatible for Editor in 1.50 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation. ---> System.NullReferenceException: Object reference not set to an instance of an object
  at Unity.Multiplayer.Playmode.Workflow.Editor.ProjectDataStore..ctor (System.String directory) [0x0006e] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Configuration\ProjectDataStore.cs:55 
  at Unity.Multiplayer.Playmode.Workflow.Editor.ProjectDataStore.GetMain () [0x00000] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Configuration\ProjectDataStore.cs:36 
  at Unity.Multiplayer.Playmode.Workflow.Editor.WorkflowMainEditorContext..ctor (Unity.Multiplayer.Playmode.VirtualProjects.Editor.MainEditorContext mainEditorContext) [0x0002c] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Contexts\WorkflowMainEditorContext.cs:15 
  at Unity.Multiplayer.Playmode.Workflow.Editor.VirtualProjectWorkflow.InitializeMPPMContexts () [0x00018] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Workflow\VirtualProjectWorkflow.cs:198 
  at Unity.Multiplayer.Playmode.Workflow.Editor.VirtualProjectWorkflow+<>c.<.cctor>b__22_0 () [0x00036] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Workflow\VirtualProjectWorkflow.cs:85 
  at Unity.Multiplayer.Playmode.VirtualProjects.Editor.EditorContexts.SendReadyEvent () [0x00038] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\VirtualProjects\Editor\Contexts\EditorContexts.cs:56 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <********************************>:0 
   --- End of inner exception stack trace ---
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x00083] in <********************************>:0 
  at System.Reflection.MethodBase.Invoke (System.Object obj, System.Object[] parameters) [0x00000] in <********************************>:0 
  at UnityEditor.EditorAssemblies.ProcessInitializeOnLoadMethodAttributes () [0x00056] in <f8e1f14ef858402482f58cef2e987b5b>:0 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  0.739 seconds
Domain Reload Profiling: 1427ms
	BeginReloadAssembly (191ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (37ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (41ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (26ms)
	LoadAllAssembliesAndSetupDomain (432ms)
		LoadAssemblies (311ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (193ms)
			TypeCache.Refresh (12ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (166ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (740ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (567ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (12ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (88ms)
			ProcessInitializeOnLoadAttributes (308ms)
			ProcessInitializeOnLoadMethodAttributes (152ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 3.51 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 37 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7342 unused Assets / (9.4 MB). Loaded Objects now: 8084.
Memory consumption went from 168.9 MB to 159.5 MB.
Total: 18.300800 ms (FindLiveObjects: 0.917000 ms CreateObjectMapping: 1.256600 ms MarkObjects: 8.733800 ms  DeleteObjects: 7.390900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.654 seconds
Refreshing native plugins compatible for Editor in 1.87 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation. ---> System.NullReferenceException: Object reference not set to an instance of an object
  at Unity.Multiplayer.Playmode.Workflow.Editor.ProjectDataStore..ctor (System.String directory) [0x0006e] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Configuration\ProjectDataStore.cs:55 
  at Unity.Multiplayer.Playmode.Workflow.Editor.ProjectDataStore.GetMain () [0x00000] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Configuration\ProjectDataStore.cs:36 
  at Unity.Multiplayer.Playmode.Workflow.Editor.WorkflowMainEditorContext..ctor (Unity.Multiplayer.Playmode.VirtualProjects.Editor.MainEditorContext mainEditorContext) [0x0002c] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Contexts\WorkflowMainEditorContext.cs:15 
  at Unity.Multiplayer.Playmode.Workflow.Editor.VirtualProjectWorkflow.InitializeMPPMContexts () [0x00018] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Workflow\VirtualProjectWorkflow.cs:198 
  at Unity.Multiplayer.Playmode.Workflow.Editor.VirtualProjectWorkflow+<>c.<.cctor>b__22_0 () [0x00036] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Workflow\VirtualProjectWorkflow.cs:85 
  at Unity.Multiplayer.Playmode.VirtualProjects.Editor.EditorContexts.SendReadyEvent () [0x00038] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\VirtualProjects\Editor\Contexts\EditorContexts.cs:56 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <********************************>:0 
   --- End of inner exception stack trace ---
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x00083] in <********************************>:0 
  at System.Reflection.MethodBase.Invoke (System.Object obj, System.Object[] parameters) [0x00000] in <********************************>:0 
  at UnityEditor.EditorAssemblies.ProcessInitializeOnLoadMethodAttributes () [0x00056] in <f8e1f14ef858402482f58cef2e987b5b>:0 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  0.711 seconds
Domain Reload Profiling: 1366ms
	BeginReloadAssembly (163ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (42ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (24ms)
	LoadAllAssembliesAndSetupDomain (429ms)
		LoadAssemblies (314ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (190ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (166ms)
			ResolveRequiredComponents (13ms)
	FinalizeReload (711ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (536ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (9ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (95ms)
			ProcessInitializeOnLoadAttributes (286ms)
			ProcessInitializeOnLoadMethodAttributes (138ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 3.35 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 37 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7342 unused Assets / (9.7 MB). Loaded Objects now: 8086.
Memory consumption went from 168.9 MB to 159.2 MB.
Total: 16.461000 ms (FindLiveObjects: 0.887900 ms CreateObjectMapping: 1.260200 ms MarkObjects: 6.719700 ms  DeleteObjects: 7.591500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 4.84 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 37 Unused Serialized files (Serialized files now loaded: 0)
Unloading 37 unused Assets / (3.7 MB). Loaded Objects now: 8086.
Memory consumption went from 163.7 MB to 160.0 MB.
Total: 13.239900 ms (FindLiveObjects: 0.599100 ms CreateObjectMapping: 0.513400 ms MarkObjects: 10.023900 ms  DeleteObjects: 2.100000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 1512364.545855 seconds.
  path: Assets/Buildings
  artifactKey: Guid(2b35ebc3e0672824388bf3bfd06f74e2) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Buildings using Guid(2b35ebc3e0672824388bf3bfd06f74e2) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c3fc3a61c9d9e666f12d422c762ffcd9') in 2.159807 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 18.098073 seconds.
  path: Assets/Buildings/Bank
  artifactKey: Guid(3adaa02d683eb144aa2e1b0c8dc9f10e) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Buildings/Bank using Guid(3adaa02d683eb144aa2e1b0c8dc9f10e) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'cf26ff4d78af1f912aa2d9ca4c33fb98') in 0.002717 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 35.344358 seconds.
  path: Assets/Buildings/Bank/Bank.prefab
  artifactKey: Guid(6931156181f04234a8c4b1a0d9e0ebcd) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Buildings/Bank/Bank.prefab using Guid(6931156181f04234a8c4b1a0d9e0ebcd) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b88d0fa19d6d139c6323758b7d938623') in 1.1674424 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 491

========================================================================
Received Import Request.
  Time since last request: 24.493925 seconds.
  path: Assets/Buildings/Bank/BankBlack.mat
  artifactKey: Guid(5dd06f98fd51ae64f9c94c8a48546e02) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Buildings/Bank/BankBlack.mat using Guid(5dd06f98fd51ae64f9c94c8a48546e02) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '65a6171469995914715ec33ab7f9af11') in 0.1392353 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 161.677292 seconds.
  path: Assets/Toon Suburban Pack/Textures/TSP_Atlas_Vegetation_1A_D.psd
  artifactKey: Guid(c29d234c2af8adb4a9571de6e14965f8) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Textures/TSP_Atlas_Vegetation_1A_D.psd using Guid(c29d234c2af8adb4a9571de6e14965f8) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '7ad5f1ba20c7398bbf864ba3d9d3f230') in 0.1197475 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 251.528039 seconds.
  path: Assets/Buildings/Bank/BankBlack.mat
  artifactKey: Guid(5dd06f98fd51ae64f9c94c8a48546e02) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Buildings/Bank/BankBlack.mat using Guid(5dd06f98fd51ae64f9c94c8a48546e02) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ed615a43d1653d40bd2a6da2d792a3db') in 0.0274401 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 87.538400 seconds.
  path: Assets/Buildings/Bank/New Material.mat
  artifactKey: Guid(677e0f914fb057f4b8307593b5be6592) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Buildings/Bank/New Material.mat using Guid(677e0f914fb057f4b8307593b5be6592) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b1a5b84af3f0e334f99a8bc81a4abd2f') in 0.0360308 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 3.17 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 37 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7324 unused Assets / (8.5 MB). Loaded Objects now: 8269.
Memory consumption went from 183.8 MB to 175.2 MB.
Total: 49.376300 ms (FindLiveObjects: 1.011400 ms CreateObjectMapping: 0.980800 ms MarkObjects: 40.227800 ms  DeleteObjects: 7.154200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  2.720 seconds
Refreshing native plugins compatible for Editor in 8.15 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation. ---> System.NullReferenceException: Object reference not set to an instance of an object
  at Unity.Multiplayer.Playmode.Workflow.Editor.ProjectDataStore..ctor (System.String directory) [0x0006e] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Configuration\ProjectDataStore.cs:55 
  at Unity.Multiplayer.Playmode.Workflow.Editor.ProjectDataStore.GetMain () [0x00000] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Configuration\ProjectDataStore.cs:36 
  at Unity.Multiplayer.Playmode.Workflow.Editor.WorkflowMainEditorContext..ctor (Unity.Multiplayer.Playmode.VirtualProjects.Editor.MainEditorContext mainEditorContext) [0x0002c] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Contexts\WorkflowMainEditorContext.cs:15 
  at Unity.Multiplayer.Playmode.Workflow.Editor.VirtualProjectWorkflow.InitializeMPPMContexts () [0x00018] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Workflow\VirtualProjectWorkflow.cs:198 
  at Unity.Multiplayer.Playmode.Workflow.Editor.VirtualProjectWorkflow+<>c.<.cctor>b__22_0 () [0x00036] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Workflow\VirtualProjectWorkflow.cs:85 
  at Unity.Multiplayer.Playmode.VirtualProjects.Editor.EditorContexts.SendReadyEvent () [0x00038] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\VirtualProjects\Editor\Contexts\EditorContexts.cs:56 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <********************************>:0 
   --- End of inner exception stack trace ---
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x00083] in <********************************>:0 
  at System.Reflection.MethodBase.Invoke (System.Object obj, System.Object[] parameters) [0x00000] in <********************************>:0 
  at UnityEditor.EditorAssemblies.ProcessInitializeOnLoadMethodAttributes () [0x00056] in <f8e1f14ef858402482f58cef2e987b5b>:0 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  2.109 seconds
Domain Reload Profiling: 4825ms
	BeginReloadAssembly (756ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (70ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (315ms)
	RebuildCommonClasses (86ms)
	RebuildNativeTypeToScriptingClass (26ms)
	initialDomainReloadingComplete (80ms)
	LoadAllAssembliesAndSetupDomain (1767ms)
		LoadAssemblies (1434ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (595ms)
			TypeCache.Refresh (26ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (514ms)
			ResolveRequiredComponents (40ms)
	FinalizeReload (2110ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1542ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (33ms)
			SetLoadedEditorAssemblies (12ms)
			BeforeProcessingInitializeOnLoad (261ms)
			ProcessInitializeOnLoadAttributes (820ms)
			ProcessInitializeOnLoadMethodAttributes (400ms)
			AfterProcessingInitializeOnLoad (15ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (30ms)
Refreshing native plugins compatible for Editor in 5.80 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 37 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7343 unused Assets / (8.9 MB). Loaded Objects now: 8157.
Memory consumption went from 180.0 MB to 171.1 MB.
Total: 20.091600 ms (FindLiveObjects: 1.372500 ms CreateObjectMapping: 1.448500 ms MarkObjects: 8.941600 ms  DeleteObjects: 8.327200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.721 seconds
Refreshing native plugins compatible for Editor in 1.85 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation. ---> System.NullReferenceException: Object reference not set to an instance of an object
  at Unity.Multiplayer.Playmode.Workflow.Editor.ProjectDataStore..ctor (System.String directory) [0x0006e] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Configuration\ProjectDataStore.cs:55 
  at Unity.Multiplayer.Playmode.Workflow.Editor.ProjectDataStore.GetMain () [0x00000] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Configuration\ProjectDataStore.cs:36 
  at Unity.Multiplayer.Playmode.Workflow.Editor.WorkflowMainEditorContext..ctor (Unity.Multiplayer.Playmode.VirtualProjects.Editor.MainEditorContext mainEditorContext) [0x0002c] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Contexts\WorkflowMainEditorContext.cs:15 
  at Unity.Multiplayer.Playmode.Workflow.Editor.VirtualProjectWorkflow.InitializeMPPMContexts () [0x00018] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Workflow\VirtualProjectWorkflow.cs:198 
  at Unity.Multiplayer.Playmode.Workflow.Editor.VirtualProjectWorkflow+<>c.<.cctor>b__22_0 () [0x00036] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Workflow\VirtualProjectWorkflow.cs:85 
  at Unity.Multiplayer.Playmode.VirtualProjects.Editor.EditorContexts.SendReadyEvent () [0x00038] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\VirtualProjects\Editor\Contexts\EditorContexts.cs:56 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <********************************>:0 
   --- End of inner exception stack trace ---
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x00083] in <********************************>:0 
  at System.Reflection.MethodBase.Invoke (System.Object obj, System.Object[] parameters) [0x00000] in <********************************>:0 
  at UnityEditor.EditorAssemblies.ProcessInitializeOnLoadMethodAttributes () [0x00056] in <f8e1f14ef858402482f58cef2e987b5b>:0 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  0.786 seconds
Domain Reload Profiling: 1508ms
	BeginReloadAssembly (183ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (54ms)
	RebuildCommonClasses (35ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (28ms)
	LoadAllAssembliesAndSetupDomain (464ms)
		LoadAssemblies (345ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (204ms)
			TypeCache.Refresh (11ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (174ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (787ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (619ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (9ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (112ms)
			ProcessInitializeOnLoadAttributes (318ms)
			ProcessInitializeOnLoadMethodAttributes (173ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 3.60 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 37 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7344 unused Assets / (8.5 MB). Loaded Objects now: 8160.
Memory consumption went from 179.9 MB to 171.3 MB.
Total: 14.991600 ms (FindLiveObjects: 0.893700 ms CreateObjectMapping: 0.995400 ms MarkObjects: 6.904800 ms  DeleteObjects: 6.195500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.690 seconds
Refreshing native plugins compatible for Editor in 1.49 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation. ---> System.NullReferenceException: Object reference not set to an instance of an object
  at Unity.Multiplayer.Playmode.Workflow.Editor.ProjectDataStore..ctor (System.String directory) [0x0006e] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Configuration\ProjectDataStore.cs:55 
  at Unity.Multiplayer.Playmode.Workflow.Editor.ProjectDataStore.GetMain () [0x00000] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Configuration\ProjectDataStore.cs:36 
  at Unity.Multiplayer.Playmode.Workflow.Editor.WorkflowMainEditorContext..ctor (Unity.Multiplayer.Playmode.VirtualProjects.Editor.MainEditorContext mainEditorContext) [0x0002c] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Contexts\WorkflowMainEditorContext.cs:15 
  at Unity.Multiplayer.Playmode.Workflow.Editor.VirtualProjectWorkflow.InitializeMPPMContexts () [0x00018] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Workflow\VirtualProjectWorkflow.cs:198 
  at Unity.Multiplayer.Playmode.Workflow.Editor.VirtualProjectWorkflow+<>c.<.cctor>b__22_0 () [0x00036] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Workflow\VirtualProjectWorkflow.cs:85 
  at Unity.Multiplayer.Playmode.VirtualProjects.Editor.EditorContexts.SendReadyEvent () [0x00038] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\VirtualProjects\Editor\Contexts\EditorContexts.cs:56 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <********************************>:0 
   --- End of inner exception stack trace ---
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x00083] in <********************************>:0 
  at System.Reflection.MethodBase.Invoke (System.Object obj, System.Object[] parameters) [0x00000] in <********************************>:0 
  at UnityEditor.EditorAssemblies.ProcessInitializeOnLoadMethodAttributes () [0x00056] in <f8e1f14ef858402482f58cef2e987b5b>:0 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  0.747 seconds
Domain Reload Profiling: 1439ms
	BeginReloadAssembly (180ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (50ms)
	RebuildCommonClasses (33ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (26ms)
	LoadAllAssembliesAndSetupDomain (443ms)
		LoadAssemblies (333ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (191ms)
			TypeCache.Refresh (11ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (165ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (748ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (568ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (9ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (93ms)
			ProcessInitializeOnLoadAttributes (293ms)
			ProcessInitializeOnLoadMethodAttributes (166ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (17ms)
Refreshing native plugins compatible for Editor in 3.12 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 37 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7344 unused Assets / (8.6 MB). Loaded Objects now: 8162.
Memory consumption went from 179.9 MB to 171.3 MB.
Total: 14.704700 ms (FindLiveObjects: 0.782200 ms CreateObjectMapping: 0.915500 ms MarkObjects: 7.055800 ms  DeleteObjects: 5.949300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.695 seconds
Refreshing native plugins compatible for Editor in 1.58 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation. ---> System.NullReferenceException: Object reference not set to an instance of an object
  at Unity.Multiplayer.Playmode.Workflow.Editor.ProjectDataStore..ctor (System.String directory) [0x0006e] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Configuration\ProjectDataStore.cs:55 
  at Unity.Multiplayer.Playmode.Workflow.Editor.ProjectDataStore.GetMain () [0x00000] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Configuration\ProjectDataStore.cs:36 
  at Unity.Multiplayer.Playmode.Workflow.Editor.WorkflowMainEditorContext..ctor (Unity.Multiplayer.Playmode.VirtualProjects.Editor.MainEditorContext mainEditorContext) [0x0002c] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Contexts\WorkflowMainEditorContext.cs:15 
  at Unity.Multiplayer.Playmode.Workflow.Editor.VirtualProjectWorkflow.InitializeMPPMContexts () [0x00018] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Workflow\VirtualProjectWorkflow.cs:198 
  at Unity.Multiplayer.Playmode.Workflow.Editor.VirtualProjectWorkflow+<>c.<.cctor>b__22_0 () [0x00036] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Workflow\VirtualProjectWorkflow.cs:85 
  at Unity.Multiplayer.Playmode.VirtualProjects.Editor.EditorContexts.SendReadyEvent () [0x00038] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\VirtualProjects\Editor\Contexts\EditorContexts.cs:56 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <********************************>:0 
   --- End of inner exception stack trace ---
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x00083] in <********************************>:0 
  at System.Reflection.MethodBase.Invoke (System.Object obj, System.Object[] parameters) [0x00000] in <********************************>:0 
  at UnityEditor.EditorAssemblies.ProcessInitializeOnLoadMethodAttributes () [0x00056] in <f8e1f14ef858402482f58cef2e987b5b>:0 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  0.720 seconds
Domain Reload Profiling: 1416ms
	BeginReloadAssembly (170ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (44ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (14ms)
	initialDomainReloadingComplete (25ms)
	LoadAllAssembliesAndSetupDomain (457ms)
		LoadAssemblies (345ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (193ms)
			TypeCache.Refresh (10ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (164ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (721ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (547ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (9ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (93ms)
			ProcessInitializeOnLoadAttributes (291ms)
			ProcessInitializeOnLoadMethodAttributes (148ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Refreshing native plugins compatible for Editor in 2.92 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 37 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7344 unused Assets / (9.4 MB). Loaded Objects now: 8164.
Memory consumption went from 179.9 MB to 170.5 MB.
Total: 15.608000 ms (FindLiveObjects: 0.938900 ms CreateObjectMapping: 1.209700 ms MarkObjects: 6.786700 ms  DeleteObjects: 6.670400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.666 seconds
Refreshing native plugins compatible for Editor in 1.63 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation. ---> System.NullReferenceException: Object reference not set to an instance of an object
  at Unity.Multiplayer.Playmode.Workflow.Editor.ProjectDataStore..ctor (System.String directory) [0x0006e] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Configuration\ProjectDataStore.cs:55 
  at Unity.Multiplayer.Playmode.Workflow.Editor.ProjectDataStore.GetMain () [0x00000] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Configuration\ProjectDataStore.cs:36 
  at Unity.Multiplayer.Playmode.Workflow.Editor.WorkflowMainEditorContext..ctor (Unity.Multiplayer.Playmode.VirtualProjects.Editor.MainEditorContext mainEditorContext) [0x0002c] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Contexts\WorkflowMainEditorContext.cs:15 
  at Unity.Multiplayer.Playmode.Workflow.Editor.VirtualProjectWorkflow.InitializeMPPMContexts () [0x00018] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Workflow\VirtualProjectWorkflow.cs:198 
  at Unity.Multiplayer.Playmode.Workflow.Editor.VirtualProjectWorkflow+<>c.<.cctor>b__22_0 () [0x00036] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Workflow\VirtualProjectWorkflow.cs:85 
  at Unity.Multiplayer.Playmode.VirtualProjects.Editor.EditorContexts.SendReadyEvent () [0x00038] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\VirtualProjects\Editor\Contexts\EditorContexts.cs:56 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <********************************>:0 
   --- End of inner exception stack trace ---
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x00083] in <********************************>:0 
  at System.Reflection.MethodBase.Invoke (System.Object obj, System.Object[] parameters) [0x00000] in <********************************>:0 
  at UnityEditor.EditorAssemblies.ProcessInitializeOnLoadMethodAttributes () [0x00056] in <f8e1f14ef858402482f58cef2e987b5b>:0 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  0.702 seconds
Domain Reload Profiling: 1369ms
	BeginReloadAssembly (161ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (42ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (26ms)
	LoadAllAssembliesAndSetupDomain (442ms)
		LoadAssemblies (329ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (188ms)
			TypeCache.Refresh (10ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (161ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (702ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (541ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (9ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (94ms)
			ProcessInitializeOnLoadAttributes (285ms)
			ProcessInitializeOnLoadMethodAttributes (146ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 3.23 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 37 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7344 unused Assets / (8.5 MB). Loaded Objects now: 8166.
Memory consumption went from 179.9 MB to 171.4 MB.
Total: 17.707700 ms (FindLiveObjects: 1.010600 ms CreateObjectMapping: 1.000100 ms MarkObjects: 8.793700 ms  DeleteObjects: 6.901500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.679 seconds
Refreshing native plugins compatible for Editor in 1.83 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation. ---> System.NullReferenceException: Object reference not set to an instance of an object
  at Unity.Multiplayer.Playmode.Workflow.Editor.ProjectDataStore..ctor (System.String directory) [0x0006e] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Configuration\ProjectDataStore.cs:55 
  at Unity.Multiplayer.Playmode.Workflow.Editor.ProjectDataStore.GetMain () [0x00000] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Configuration\ProjectDataStore.cs:36 
  at Unity.Multiplayer.Playmode.Workflow.Editor.WorkflowMainEditorContext..ctor (Unity.Multiplayer.Playmode.VirtualProjects.Editor.MainEditorContext mainEditorContext) [0x0002c] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Contexts\WorkflowMainEditorContext.cs:15 
  at Unity.Multiplayer.Playmode.Workflow.Editor.VirtualProjectWorkflow.InitializeMPPMContexts () [0x00018] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Workflow\VirtualProjectWorkflow.cs:198 
  at Unity.Multiplayer.Playmode.Workflow.Editor.VirtualProjectWorkflow+<>c.<.cctor>b__22_0 () [0x00036] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Workflow\VirtualProjectWorkflow.cs:85 
  at Unity.Multiplayer.Playmode.VirtualProjects.Editor.EditorContexts.SendReadyEvent () [0x00038] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\VirtualProjects\Editor\Contexts\EditorContexts.cs:56 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <********************************>:0 
   --- End of inner exception stack trace ---
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x00083] in <********************************>:0 
  at System.Reflection.MethodBase.Invoke (System.Object obj, System.Object[] parameters) [0x00000] in <********************************>:0 
  at UnityEditor.EditorAssemblies.ProcessInitializeOnLoadMethodAttributes () [0x00056] in <f8e1f14ef858402482f58cef2e987b5b>:0 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  0.732 seconds
Domain Reload Profiling: 1412ms
	BeginReloadAssembly (166ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (46ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (25ms)
	LoadAllAssembliesAndSetupDomain (449ms)
		LoadAssemblies (333ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (190ms)
			TypeCache.Refresh (10ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (164ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (732ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (560ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (9ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (96ms)
			ProcessInitializeOnLoadAttributes (302ms)
			ProcessInitializeOnLoadMethodAttributes (147ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 3.34 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 37 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7344 unused Assets / (8.8 MB). Loaded Objects now: 8168.
Memory consumption went from 179.9 MB to 171.1 MB.
Total: 17.463800 ms (FindLiveObjects: 0.980300 ms CreateObjectMapping: 1.192100 ms MarkObjects: 8.658200 ms  DeleteObjects: 6.629200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.686 seconds
Refreshing native plugins compatible for Editor in 1.54 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation. ---> System.NullReferenceException: Object reference not set to an instance of an object
  at Unity.Multiplayer.Playmode.Workflow.Editor.ProjectDataStore..ctor (System.String directory) [0x0006e] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Configuration\ProjectDataStore.cs:55 
  at Unity.Multiplayer.Playmode.Workflow.Editor.ProjectDataStore.GetMain () [0x00000] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Configuration\ProjectDataStore.cs:36 
  at Unity.Multiplayer.Playmode.Workflow.Editor.WorkflowMainEditorContext..ctor (Unity.Multiplayer.Playmode.VirtualProjects.Editor.MainEditorContext mainEditorContext) [0x0002c] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Contexts\WorkflowMainEditorContext.cs:15 
  at Unity.Multiplayer.Playmode.Workflow.Editor.VirtualProjectWorkflow.InitializeMPPMContexts () [0x00018] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Workflow\VirtualProjectWorkflow.cs:198 
  at Unity.Multiplayer.Playmode.Workflow.Editor.VirtualProjectWorkflow+<>c.<.cctor>b__22_0 () [0x00036] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Workflow\VirtualProjectWorkflow.cs:85 
  at Unity.Multiplayer.Playmode.VirtualProjects.Editor.EditorContexts.SendReadyEvent () [0x00038] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\VirtualProjects\Editor\Contexts\EditorContexts.cs:56 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <********************************>:0 
   --- End of inner exception stack trace ---
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x00083] in <********************************>:0 
  at System.Reflection.MethodBase.Invoke (System.Object obj, System.Object[] parameters) [0x00000] in <********************************>:0 
  at UnityEditor.EditorAssemblies.ProcessInitializeOnLoadMethodAttributes () [0x00056] in <f8e1f14ef858402482f58cef2e987b5b>:0 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  0.731 seconds
Domain Reload Profiling: 1418ms
	BeginReloadAssembly (177ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (43ms)
	RebuildCommonClasses (31ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (25ms)
	LoadAllAssembliesAndSetupDomain (443ms)
		LoadAssemblies (336ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (194ms)
			TypeCache.Refresh (10ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (167ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (731ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (566ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (13ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (101ms)
			ProcessInitializeOnLoadAttributes (295ms)
			ProcessInitializeOnLoadMethodAttributes (151ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 2.69 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 37 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7344 unused Assets / (8.8 MB). Loaded Objects now: 8170.
Memory consumption went from 179.9 MB to 171.2 MB.
Total: 16.159800 ms (FindLiveObjects: 0.799700 ms CreateObjectMapping: 0.995300 ms MarkObjects: 7.373600 ms  DeleteObjects: 6.986900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.666 seconds
Refreshing native plugins compatible for Editor in 1.64 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation. ---> System.NullReferenceException: Object reference not set to an instance of an object
  at Unity.Multiplayer.Playmode.Workflow.Editor.ProjectDataStore..ctor (System.String directory) [0x0006e] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Configuration\ProjectDataStore.cs:55 
  at Unity.Multiplayer.Playmode.Workflow.Editor.ProjectDataStore.GetMain () [0x00000] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Configuration\ProjectDataStore.cs:36 
  at Unity.Multiplayer.Playmode.Workflow.Editor.WorkflowMainEditorContext..ctor (Unity.Multiplayer.Playmode.VirtualProjects.Editor.MainEditorContext mainEditorContext) [0x0002c] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Contexts\WorkflowMainEditorContext.cs:15 
  at Unity.Multiplayer.Playmode.Workflow.Editor.VirtualProjectWorkflow.InitializeMPPMContexts () [0x00018] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Workflow\VirtualProjectWorkflow.cs:198 
  at Unity.Multiplayer.Playmode.Workflow.Editor.VirtualProjectWorkflow+<>c.<.cctor>b__22_0 () [0x00036] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Workflow\VirtualProjectWorkflow.cs:85 
  at Unity.Multiplayer.Playmode.VirtualProjects.Editor.EditorContexts.SendReadyEvent () [0x00038] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\VirtualProjects\Editor\Contexts\EditorContexts.cs:56 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <********************************>:0 
   --- End of inner exception stack trace ---
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x00083] in <********************************>:0 
  at System.Reflection.MethodBase.Invoke (System.Object obj, System.Object[] parameters) [0x00000] in <********************************>:0 
  at UnityEditor.EditorAssemblies.ProcessInitializeOnLoadMethodAttributes () [0x00056] in <f8e1f14ef858402482f58cef2e987b5b>:0 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  0.703 seconds
Domain Reload Profiling: 1371ms
	BeginReloadAssembly (171ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (8ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (45ms)
	RebuildCommonClasses (27ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (27ms)
	LoadAllAssembliesAndSetupDomain (432ms)
		LoadAssemblies (315ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (190ms)
			TypeCache.Refresh (10ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (164ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (704ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (544ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (11ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (91ms)
			ProcessInitializeOnLoadAttributes (291ms)
			ProcessInitializeOnLoadMethodAttributes (145ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 3.44 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 37 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7344 unused Assets / (9.0 MB). Loaded Objects now: 8172.
Memory consumption went from 179.9 MB to 170.9 MB.
Total: 17.204000 ms (FindLiveObjects: 1.627800 ms CreateObjectMapping: 1.352800 ms MarkObjects: 7.554500 ms  DeleteObjects: 6.666600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in 12.178 seconds
Refreshing native plugins compatible for Editor in 1.48 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation. ---> System.NullReferenceException: Object reference not set to an instance of an object
  at Unity.Multiplayer.Playmode.Workflow.Editor.ProjectDataStore..ctor (System.String directory) [0x0006e] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Configuration\ProjectDataStore.cs:55 
  at Unity.Multiplayer.Playmode.Workflow.Editor.ProjectDataStore.GetMain () [0x00000] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Configuration\ProjectDataStore.cs:36 
  at Unity.Multiplayer.Playmode.Workflow.Editor.WorkflowMainEditorContext..ctor (Unity.Multiplayer.Playmode.VirtualProjects.Editor.MainEditorContext mainEditorContext) [0x0002c] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Contexts\WorkflowMainEditorContext.cs:15 
  at Unity.Multiplayer.Playmode.Workflow.Editor.VirtualProjectWorkflow.InitializeMPPMContexts () [0x00018] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Workflow\VirtualProjectWorkflow.cs:198 
  at Unity.Multiplayer.Playmode.Workflow.Editor.VirtualProjectWorkflow+<>c.<.cctor>b__22_0 () [0x00036] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Workflow\VirtualProjectWorkflow.cs:85 
  at Unity.Multiplayer.Playmode.VirtualProjects.Editor.EditorContexts.SendReadyEvent () [0x00038] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\VirtualProjects\Editor\Contexts\EditorContexts.cs:56 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <********************************>:0 
   --- End of inner exception stack trace ---
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x00083] in <********************************>:0 
  at System.Reflection.MethodBase.Invoke (System.Object obj, System.Object[] parameters) [0x00000] in <********************************>:0 
  at UnityEditor.EditorAssemblies.ProcessInitializeOnLoadMethodAttributes () [0x00056] in <f8e1f14ef858402482f58cef2e987b5b>:0 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  0.696 seconds
Domain Reload Profiling: 12875ms
	BeginReloadAssembly (723ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (48ms)
	RebuildCommonClasses (253ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (26ms)
	LoadAllAssembliesAndSetupDomain (11167ms)
		LoadAssemblies (10500ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (1293ms)
			TypeCache.Refresh (11ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (1266ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (696ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (534ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (9ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (92ms)
			ProcessInitializeOnLoadAttributes (276ms)
			ProcessInitializeOnLoadMethodAttributes (150ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 3.14 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 37 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7344 unused Assets / (8.3 MB). Loaded Objects now: 8174.
Memory consumption went from 179.9 MB to 171.6 MB.
Total: 9.971600 ms (FindLiveObjects: 0.655100 ms CreateObjectMapping: 0.525900 ms MarkObjects: 4.531100 ms  DeleteObjects: 4.258300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 2.92 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 37 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7325 unused Assets / (8.2 MB). Loaded Objects now: 8175.
Memory consumption went from 180.0 MB to 171.8 MB.
Total: 16.100600 ms (FindLiveObjects: 0.812000 ms CreateObjectMapping: 1.032300 ms MarkObjects: 8.519100 ms  DeleteObjects: 5.734900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.423 seconds
Refreshing native plugins compatible for Editor in 11.06 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation. ---> System.NullReferenceException: Object reference not set to an instance of an object
  at Unity.Multiplayer.Playmode.Workflow.Editor.ProjectDataStore..ctor (System.String directory) [0x0006e] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Configuration\ProjectDataStore.cs:55 
  at Unity.Multiplayer.Playmode.Workflow.Editor.ProjectDataStore.GetMain () [0x00000] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Configuration\ProjectDataStore.cs:36 
  at Unity.Multiplayer.Playmode.Workflow.Editor.WorkflowMainEditorContext..ctor (Unity.Multiplayer.Playmode.VirtualProjects.Editor.MainEditorContext mainEditorContext) [0x0002c] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Contexts\WorkflowMainEditorContext.cs:15 
  at Unity.Multiplayer.Playmode.Workflow.Editor.VirtualProjectWorkflow.InitializeMPPMContexts () [0x00018] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Workflow\VirtualProjectWorkflow.cs:198 
  at Unity.Multiplayer.Playmode.Workflow.Editor.VirtualProjectWorkflow+<>c.<.cctor>b__22_0 () [0x00036] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Workflow\VirtualProjectWorkflow.cs:85 
  at Unity.Multiplayer.Playmode.VirtualProjects.Editor.EditorContexts.SendReadyEvent () [0x00038] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\VirtualProjects\Editor\Contexts\EditorContexts.cs:56 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <********************************>:0 
   --- End of inner exception stack trace ---
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x00083] in <********************************>:0 
  at System.Reflection.MethodBase.Invoke (System.Object obj, System.Object[] parameters) [0x00000] in <********************************>:0 
  at UnityEditor.EditorAssemblies.ProcessInitializeOnLoadMethodAttributes () [0x00056] in <f8e1f14ef858402482f58cef2e987b5b>:0 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  1.902 seconds
Domain Reload Profiling: 3323ms
	BeginReloadAssembly (329ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (22ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (81ms)
	RebuildCommonClasses (59ms)
	RebuildNativeTypeToScriptingClass (24ms)
	initialDomainReloadingComplete (53ms)
	LoadAllAssembliesAndSetupDomain (954ms)
		LoadAssemblies (732ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (385ms)
			TypeCache.Refresh (19ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (328ms)
			ResolveRequiredComponents (31ms)
	FinalizeReload (1903ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1464ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (30ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (235ms)
			ProcessInitializeOnLoadAttributes (776ms)
			ProcessInitializeOnLoadMethodAttributes (407ms)
			AfterProcessingInitializeOnLoad (9ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (31ms)
Refreshing native plugins compatible for Editor in 5.53 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 37 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7345 unused Assets / (8.9 MB). Loaded Objects now: 8177.
Memory consumption went from 179.9 MB to 171.0 MB.
Total: 18.639700 ms (FindLiveObjects: 1.356300 ms CreateObjectMapping: 1.086400 ms MarkObjects: 8.705100 ms  DeleteObjects: 7.488000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.595 seconds
Refreshing native plugins compatible for Editor in 4.52 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation. ---> System.NullReferenceException: Object reference not set to an instance of an object
  at Unity.Multiplayer.Playmode.Workflow.Editor.ProjectDataStore..ctor (System.String directory) [0x0006e] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Configuration\ProjectDataStore.cs:55 
  at Unity.Multiplayer.Playmode.Workflow.Editor.ProjectDataStore.GetMain () [0x00000] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Configuration\ProjectDataStore.cs:36 
  at Unity.Multiplayer.Playmode.Workflow.Editor.WorkflowMainEditorContext..ctor (Unity.Multiplayer.Playmode.VirtualProjects.Editor.MainEditorContext mainEditorContext) [0x0002c] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Contexts\WorkflowMainEditorContext.cs:15 
  at Unity.Multiplayer.Playmode.Workflow.Editor.VirtualProjectWorkflow.InitializeMPPMContexts () [0x00018] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Workflow\VirtualProjectWorkflow.cs:198 
  at Unity.Multiplayer.Playmode.Workflow.Editor.VirtualProjectWorkflow+<>c.<.cctor>b__22_0 () [0x00036] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Workflow\VirtualProjectWorkflow.cs:85 
  at Unity.Multiplayer.Playmode.VirtualProjects.Editor.EditorContexts.SendReadyEvent () [0x00038] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\VirtualProjects\Editor\Contexts\EditorContexts.cs:56 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <********************************>:0 
   --- End of inner exception stack trace ---
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x00083] in <********************************>:0 
  at System.Reflection.MethodBase.Invoke (System.Object obj, System.Object[] parameters) [0x00000] in <********************************>:0 
  at UnityEditor.EditorAssemblies.ProcessInitializeOnLoadMethodAttributes () [0x00056] in <f8e1f14ef858402482f58cef2e987b5b>:0 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  1.850 seconds
Domain Reload Profiling: 3440ms
	BeginReloadAssembly (361ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (16ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (80ms)
	RebuildCommonClasses (62ms)
	RebuildNativeTypeToScriptingClass (25ms)
	initialDomainReloadingComplete (57ms)
	LoadAllAssembliesAndSetupDomain (1083ms)
		LoadAssemblies (752ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (515ms)
			TypeCache.Refresh (28ms)
				TypeCache.ScanAssembly (3ms)
			BuildScriptInfoCaches (447ms)
			ResolveRequiredComponents (32ms)
	FinalizeReload (1851ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1433ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (29ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (229ms)
			ProcessInitializeOnLoadAttributes (774ms)
			ProcessInitializeOnLoadMethodAttributes (384ms)
			AfterProcessingInitializeOnLoad (11ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (29ms)
Refreshing native plugins compatible for Editor in 4.78 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 37 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7345 unused Assets / (9.1 MB). Loaded Objects now: 8179.
Memory consumption went from 180.0 MB to 170.9 MB.
Total: 19.058800 ms (FindLiveObjects: 1.104300 ms CreateObjectMapping: 1.449800 ms MarkObjects: 8.916100 ms  DeleteObjects: 7.586900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.679 seconds
Refreshing native plugins compatible for Editor in 5.24 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation. ---> System.NullReferenceException: Object reference not set to an instance of an object
  at Unity.Multiplayer.Playmode.Workflow.Editor.ProjectDataStore..ctor (System.String directory) [0x0006e] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Configuration\ProjectDataStore.cs:55 
  at Unity.Multiplayer.Playmode.Workflow.Editor.ProjectDataStore.GetMain () [0x00000] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Configuration\ProjectDataStore.cs:36 
  at Unity.Multiplayer.Playmode.Workflow.Editor.WorkflowMainEditorContext..ctor (Unity.Multiplayer.Playmode.VirtualProjects.Editor.MainEditorContext mainEditorContext) [0x0002c] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Contexts\WorkflowMainEditorContext.cs:15 
  at Unity.Multiplayer.Playmode.Workflow.Editor.VirtualProjectWorkflow.InitializeMPPMContexts () [0x00018] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Workflow\VirtualProjectWorkflow.cs:198 
  at Unity.Multiplayer.Playmode.Workflow.Editor.VirtualProjectWorkflow+<>c.<.cctor>b__22_0 () [0x00036] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Workflow\VirtualProjectWorkflow.cs:85 
  at Unity.Multiplayer.Playmode.VirtualProjects.Editor.EditorContexts.SendReadyEvent () [0x00038] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\VirtualProjects\Editor\Contexts\EditorContexts.cs:56 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <********************************>:0 
   --- End of inner exception stack trace ---
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x00083] in <********************************>:0 
  at System.Reflection.MethodBase.Invoke (System.Object obj, System.Object[] parameters) [0x00000] in <********************************>:0 
  at UnityEditor.EditorAssemblies.ProcessInitializeOnLoadMethodAttributes () [0x00056] in <f8e1f14ef858402482f58cef2e987b5b>:0 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  1.990 seconds
Domain Reload Profiling: 3665ms
	BeginReloadAssembly (318ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (8ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (65ms)
	RebuildCommonClasses (61ms)
	RebuildNativeTypeToScriptingClass (29ms)
	initialDomainReloadingComplete (65ms)
	LoadAllAssembliesAndSetupDomain (1200ms)
		LoadAssemblies (903ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (491ms)
			TypeCache.Refresh (24ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (427ms)
			ResolveRequiredComponents (32ms)
	FinalizeReload (1991ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1519ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (27ms)
			SetLoadedEditorAssemblies (15ms)
			BeforeProcessingInitializeOnLoad (262ms)
			ProcessInitializeOnLoadAttributes (815ms)
			ProcessInitializeOnLoadMethodAttributes (390ms)
			AfterProcessingInitializeOnLoad (9ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (28ms)
Refreshing native plugins compatible for Editor in 6.09 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 37 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7350 unused Assets / (8.7 MB). Loaded Objects now: 8186.
Memory consumption went from 180.0 MB to 171.3 MB.
Total: 19.091600 ms (FindLiveObjects: 1.267900 ms CreateObjectMapping: 1.235200 ms MarkObjects: 8.575000 ms  DeleteObjects: 8.010600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 40624.690583 seconds.
  path: Assets/Trees/Prefabs/URP/BigTree.prefab
  artifactKey: Guid(90d0236f9b7037a4c98e9f02a79e2bee) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Trees/Prefabs/URP/BigTree.prefab using Guid(90d0236f9b7037a4c98e9f02a79e2bee) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b80c58768a5436efca2632cc77b006f6') in 1.2311552 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 14

========================================================================
Received Import Request.
  Time since last request: 2.622769 seconds.
  path: Assets/Trees/Source/Materials/URP/Falling_Leaves/Bambo.mat
  artifactKey: Guid(92e39ba4c6322b743898a08eb9129e37) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Trees/Source/Materials/URP/Falling_Leaves/Bambo.mat using Guid(92e39ba4c6322b743898a08eb9129e37) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '9ffa8e4344bbebd310e3931e8cb6808f') in 0.0620826 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 4

========================================================================
Received Import Request.
  Time since last request: 64.768580 seconds.
  path: Assets/Trees/Source/Materials/URP/Leaves
  artifactKey: Guid(2687f1cf1e325ba49b589c61a49032ea) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Trees/Source/Materials/URP/Leaves using Guid(2687f1cf1e325ba49b589c61a49032ea) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '4ef11ee622b5cb0192fdbfca709663a5') in 0.0035042 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.008113 seconds.
  path: Assets/Trees/Source/Materials/URP/Leaves/Bambo_Leaves.mat
  artifactKey: Guid(aebd5e7894f33384ba6e9c3456f67857) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Trees/Source/Materials/URP/Leaves/Bambo_Leaves.mat using Guid(aebd5e7894f33384ba6e9c3456f67857) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '45ef531c39d55916f0d80c61f923581f') in 2.9690193 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.491 seconds
Refreshing native plugins compatible for Editor in 1.75 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation. ---> System.NullReferenceException: Object reference not set to an instance of an object
  at Unity.Multiplayer.Playmode.Workflow.Editor.ProjectDataStore..ctor (System.String directory) [0x0006e] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Configuration\ProjectDataStore.cs:55 
  at Unity.Multiplayer.Playmode.Workflow.Editor.ProjectDataStore.GetMain () [0x00000] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Configuration\ProjectDataStore.cs:36 
  at Unity.Multiplayer.Playmode.Workflow.Editor.WorkflowMainEditorContext..ctor (Unity.Multiplayer.Playmode.VirtualProjects.Editor.MainEditorContext mainEditorContext) [0x0002c] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Contexts\WorkflowMainEditorContext.cs:15 
  at Unity.Multiplayer.Playmode.Workflow.Editor.VirtualProjectWorkflow.InitializeMPPMContexts () [0x00018] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Workflow\VirtualProjectWorkflow.cs:198 
  at Unity.Multiplayer.Playmode.Workflow.Editor.VirtualProjectWorkflow+<>c.<.cctor>b__22_0 () [0x00036] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Workflow\VirtualProjectWorkflow.cs:85 
  at Unity.Multiplayer.Playmode.VirtualProjects.Editor.EditorContexts.SendReadyEvent () [0x00038] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\VirtualProjects\Editor\Contexts\EditorContexts.cs:56 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <********************************>:0 
   --- End of inner exception stack trace ---
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x00083] in <********************************>:0 
  at System.Reflection.MethodBase.Invoke (System.Object obj, System.Object[] parameters) [0x00000] in <********************************>:0 
  at UnityEditor.EditorAssemblies.ProcessInitializeOnLoadMethodAttributes () [0x00056] in <f8e1f14ef858402482f58cef2e987b5b>:0 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  0.939 seconds
Domain Reload Profiling: 2432ms
	BeginReloadAssembly (360ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (27ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (148ms)
	RebuildCommonClasses (35ms)
	RebuildNativeTypeToScriptingClass (13ms)
	initialDomainReloadingComplete (30ms)
	LoadAllAssembliesAndSetupDomain (1054ms)
		LoadAssemblies (932ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (239ms)
			TypeCache.Refresh (10ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (210ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (940ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (690ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (14ms)
			SetLoadedEditorAssemblies (9ms)
			BeforeProcessingInitializeOnLoad (134ms)
			ProcessInitializeOnLoadAttributes (348ms)
			ProcessInitializeOnLoadMethodAttributes (180ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (14ms)
Refreshing native plugins compatible for Editor in 4.54 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 38 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7351 unused Assets / (8.3 MB). Loaded Objects now: 8209.
Memory consumption went from 185.0 MB to 176.6 MB.
Total: 22.270000 ms (FindLiveObjects: 1.098000 ms CreateObjectMapping: 1.162900 ms MarkObjects: 11.584400 ms  DeleteObjects: 8.422500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 1159.068878 seconds.
  path: Assets/Trees/Source/Materials/URP/Leaves/Bambo_Leaves.mat
  artifactKey: Guid(aebd5e7894f33384ba6e9c3456f67857) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Trees/Source/Materials/URP/Leaves/Bambo_Leaves.mat using Guid(aebd5e7894f33384ba6e9c3456f67857) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ac4ee6ceb83433c15753e74a7a859c45') in 0.7431344 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 12.84 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 37 Unused Serialized files (Serialized files now loaded: 0)
Unloading 37 unused Assets / (3.7 MB). Loaded Objects now: 8343.
Memory consumption went from 187.0 MB to 183.3 MB.
Total: 17.929500 ms (FindLiveObjects: 0.725100 ms CreateObjectMapping: 0.423400 ms MarkObjects: 14.768800 ms  DeleteObjects: 2.010000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 5061.154331 seconds.
  path: Assets/Toon Suburban Pack/Scenes/Sample Scene.unity
  artifactKey: Guid(609d223574134cb4893fe24a6220939b) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Scenes/Sample Scene.unity using Guid(609d223574134cb4893fe24a6220939b) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '6011f9b33cb2e0e85f058734115742d4') in 0.0121532 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 18.345623 seconds.
  path: Assets/Toon Suburban Pack/Scenes/Draft_MainMap.unity
  artifactKey: Guid(609d223574134cb4893fe24a6220939b) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Scenes/Draft_MainMap.unity using Guid(609d223574134cb4893fe24a6220939b) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '28db1626aaca7c70a783ec48db49dd44') in 0.0007841 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 2373.688870 seconds.
  path: Assets/Toon Suburban Pack/Terrain/Layer_Terrain_Asphalt_1A_D.terrainlayer
  artifactKey: Guid(da299f75ebc218c41b84c5abe76e3854) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Terrain/Layer_Terrain_Asphalt_1A_D.terrainlayer using Guid(da299f75ebc218c41b84c5abe76e3854) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'adcedd319990d48615e5ff41998435f7') in 0.2775168 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000293 seconds.
  path: Assets/Mirror/Examples/PlayerTest/PlayerTestScene/TerrainLayer2019.terrainlayer
  artifactKey: Guid(626c6e9ff4b955443ae42a354ab1f38a) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Mirror/Examples/PlayerTest/PlayerTestScene/TerrainLayer2019.terrainlayer using Guid(626c6e9ff4b955443ae42a354ab1f38a) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '5e21bffbd61d93946cb9018717aea242') in 0.019976 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Toon Suburban Pack/Terrain/Layer_Terrain_Grass_1A_D.terrainlayer
  artifactKey: Guid(89602d070e3001a4285f7926c7f63209) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Terrain/Layer_Terrain_Grass_1A_D.terrainlayer using Guid(89602d070e3001a4285f7926c7f63209) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '30bda77ff7b9f9a70ed5bfebe2542d25') in 0.0531198 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 10.860618 seconds.
  path: Assets/Toon Suburban Pack/Terrain/Terrain_Grass_1A_D.tif
  artifactKey: Guid(5b31f3103d86bdc4896cb7b23b9470f2) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Terrain/Terrain_Grass_1A_D.tif using Guid(5b31f3103d86bdc4896cb7b23b9470f2) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd0c4acf1d2a4f06d8ea2a97aa61140f6') in 0.0624577 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 13.700171 seconds.
  path: Assets/Toon Suburban Pack/Terrain/Terrain_Grass_2A_D.tif
  artifactKey: Guid(655cdf0df4ff14741aff4991f9a331b1) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Terrain/Terrain_Grass_2A_D.tif using Guid(655cdf0df4ff14741aff4991f9a331b1) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c7b2bf0762fb00f7e7520201c51fb87e') in 0.030071 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 51.360087 seconds.
  path: Assets/Toon Suburban Pack/Terrain/Terrain_Earth_2A_D.tif
  artifactKey: Guid(01508567b8f8c6140bff33aabbe2c3b3) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Terrain/Terrain_Earth_2A_D.tif using Guid(01508567b8f8c6140bff33aabbe2c3b3) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0c06f3964a177278a62c5351a60e3497') in 0.0268849 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 2.926939 seconds.
  path: Assets/Toon Suburban Pack/Terrain/Terrain_Earth_1A_D.tif
  artifactKey: Guid(6ce589edd7bf23e48910e798a0db3687) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Terrain/Terrain_Earth_1A_D.tif using Guid(6ce589edd7bf23e48910e798a0db3687) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '685fe173c65dbd386459ae6b00dd0972') in 0.0288672 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 2.775311 seconds.
  path: Assets/Toon Suburban Pack/Terrain/Terrain_Asphalt_1A_D.tif
  artifactKey: Guid(407c439d6e854a9439220e8288de8030) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Terrain/Terrain_Asphalt_1A_D.tif using Guid(407c439d6e854a9439220e8288de8030) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c453526684e72b4387ac890e55066afe') in 0.0317923 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 502.442058 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Antenna_01A.fbx
  artifactKey: Guid(58bbc7f26709bd845a557fbc03ef81e2) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Antenna_01A.fbx using Guid(58bbc7f26709bd845a557fbc03ef81e2) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f117c1fe6feb77c8205d5d2ebd122043') in 0.3897412 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 17

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Armchair_01A.fbx
  artifactKey: Guid(879a2304d95fb9d4a89fec70af74e0d5) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Armchair_01A.fbx using Guid(879a2304d95fb9d4a89fec70af74e0d5) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '623c45f763fbc4adf598b7c814f5b549') in 0.05262 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Armchair_01B.fbx
  artifactKey: Guid(60b206666f716324f859f8a984404d29) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Armchair_01B.fbx using Guid(60b206666f716324f859f8a984404d29) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'bf9f96a7aae69405bcb46927f9bf01cd') in 0.0355817 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Bathroom_Toilet_Paper_01A.fbx
  artifactKey: Guid(a31ab163b673dc0429cf88fbe6108304) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Bathroom_Toilet_Paper_01A.fbx using Guid(a31ab163b673dc0429cf88fbe6108304) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b187afd52bb9c177de89902a6f87e5f4') in 0.0267965 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Bathroom_Towel_01A.fbx
  artifactKey: Guid(ca75c64a50be03542b8370da8d6e81da) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Bathroom_Towel_01A.fbx using Guid(ca75c64a50be03542b8370da8d6e81da) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '07b7831a89ed474d8141fa7845d77269') in 0.0291184 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Bathroom_Shower_Cabin_01B.fbx
  artifactKey: Guid(416adae4b687c9f49832d606a0310070) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Bathroom_Shower_Cabin_01B.fbx using Guid(416adae4b687c9f49832d606a0310070) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f0a49a9a21aad05c7529abe619477089') in 0.0915394 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 23

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Bathroom_Toilet_02A.fbx
  artifactKey: Guid(835a44b23fdb80a448094cc650b52969) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Bathroom_Toilet_02A.fbx using Guid(835a44b23fdb80a448094cc650b52969) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '931f987d646b90bfd3830c0c9ec28f22') in 0.0360858 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000067 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Bathroom_Toilet_02B.fbx
  artifactKey: Guid(d94dde6d11c238f4485d48ca785e8de6) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Bathroom_Toilet_02B.fbx using Guid(d94dde6d11c238f4485d48ca785e8de6) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0c9f35824350b059f99e10eee0546c5f') in 0.0385511 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Bathroom_Sink_01B.fbx
  artifactKey: Guid(c80875c3229c2ad45baf281387b04d07) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Bathroom_Sink_01B.fbx using Guid(c80875c3229c2ad45baf281387b04d07) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '34e5ce8cdf915012be03640daf743f63') in 0.0293834 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Armchair_01C.fbx
  artifactKey: Guid(04f348d35e57eea4c94fcaa59e3c120f) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Armchair_01C.fbx using Guid(04f348d35e57eea4c94fcaa59e3c120f) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '584f73053bd50a901fd244bc5c4f48cf') in 0.0277854 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Bathroom_Shower_Cabin_01A.fbx
  artifactKey: Guid(6b39d46d554f7cc46bf79787f341aa96) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Bathroom_Shower_Cabin_01A.fbx using Guid(6b39d46d554f7cc46bf79787f341aa96) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '649746faeffd9df320b25bcfa1d2bd4d') in 0.0511126 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 23

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Bathroom_Towel_01D.fbx
  artifactKey: Guid(0ae033643fb6eb04c9a9bc550b790459) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Bathroom_Towel_01D.fbx using Guid(0ae033643fb6eb04c9a9bc550b790459) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f2dfafe86c5a39e2f3f4b3d1b9a74d4a') in 0.0283772 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Bathroom_Towel_01C.fbx
  artifactKey: Guid(3df830dd213ebb94bbe2aa7e96f68a44) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Bathroom_Towel_01C.fbx using Guid(3df830dd213ebb94bbe2aa7e96f68a44) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'de1eec104007dfe12addf2a1d59ea856') in 0.0213951 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Bathroom_Sink_02A.fbx
  artifactKey: Guid(d221456ccdafd1d49a61e2f2f9cb88bb) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Bathroom_Sink_02A.fbx using Guid(d221456ccdafd1d49a61e2f2f9cb88bb) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '2b619ff640f164745dd2f8dc6b6db45d') in 0.0272992 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Bathroom_Sink_01A.fbx
  artifactKey: Guid(84a90e2063f46cf47877791873380fb4) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Bathroom_Sink_01A.fbx using Guid(84a90e2063f46cf47877791873380fb4) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '3fae2fcdb02d104927074bd9c283608c') in 0.0273647 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 9.237964 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Characters Variants/TSP_Female_Character_01B.prefab
  artifactKey: Guid(31d965b36d81fc34c90caa0278bb3440) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Characters Variants/TSP_Female_Character_01B.prefab using Guid(31d965b36d81fc34c90caa0278bb3440) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c9ab9e4f5ff3ded6103c3e296d0cfc95') in 0.2516628 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 178

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Characters Variants/TSP_Female_Character_03B.prefab
  artifactKey: Guid(be2969404cd54a24c83930d0624ac76d) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Characters Variants/TSP_Female_Character_03B.prefab using Guid(be2969404cd54a24c83930d0624ac76d) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '086cb3e44b2f273349d1a21e1c988658') in 0.0676385 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 188

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Characters Variants/TSP_Male_Character_01B.prefab
  artifactKey: Guid(d5eed18bad6c6ca4b8f9b64eeb5ff205) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Characters Variants/TSP_Male_Character_01B.prefab using Guid(d5eed18bad6c6ca4b8f9b64eeb5ff205) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '47830a4e52f45ebdc8d52007b521e7cf') in 0.0439494 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 174

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Characters Variants/TSP_Male_Character_02C.prefab
  artifactKey: Guid(4ef6b1e8ec173e94191d9aa742d1f863) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Characters Variants/TSP_Male_Character_02C.prefab using Guid(4ef6b1e8ec173e94191d9aa742d1f863) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f83dd741e49fa21a3f8422a84b15d359') in 0.0328569 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 164

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Characters Variants/TSP_Male_Character_02B.prefab
  artifactKey: Guid(a361f300db040ac488375aac22667b1e) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Characters Variants/TSP_Male_Character_02B.prefab using Guid(a361f300db040ac488375aac22667b1e) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '73e9251c4933226da20c21660c5db84c') in 0.0518522 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 164

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Characters Variants/TSP_Male_Character_03B.prefab
  artifactKey: Guid(cf04c4d295c0fed40b44a62887b435aa) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Characters Variants/TSP_Male_Character_03B.prefab using Guid(cf04c4d295c0fed40b44a62887b435aa) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'bf067b45b409259a9841c5fc06d59999') in 0.0282587 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 178

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Characters Variants/TSP_Female_Character_03A.prefab
  artifactKey: Guid(fa99ac7bfbf39f04ca348423e2798ff1) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Characters Variants/TSP_Female_Character_03A.prefab using Guid(fa99ac7bfbf39f04ca348423e2798ff1) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ae4cfc04ff1d79a2a3d38936666ba15a') in 0.0322176 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 188

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Characters Variants/TSP_Male_Character_01D.prefab
  artifactKey: Guid(cf553440dc5aa254fb0720ac042139d7) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Characters Variants/TSP_Male_Character_01D.prefab using Guid(cf553440dc5aa254fb0720ac042139d7) Importer(*********,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'de2668e6fea67cc0cd0f9c1b67c78c7e') in 0.0281232 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 174

Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0