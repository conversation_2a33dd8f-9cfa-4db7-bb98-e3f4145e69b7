Using pre-set license
Built from '6000.0/staging' branch; Version is '6000.0.47f1 (2ad1ed33fd3b) revision 2806253'; Using compiler version '193933523'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit CoreSingleLanguage' Language: 'fr' Physical Memory: 16051 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker53
-projectPath
C:/Users/<USER>/ONU
-logFile
Logs/AssetImportWorker53.log
-srvPort
56204
-job-worker-count
7
-background-job-worker-count
8
-gc-helper-count
1
Successfully changed project path to: C:/Users/<USER>/ONU
C:/Users/<USER>/ONU
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [48212]  Target information:

Player connection [48212]  * "[IP] ************** [Port] 0 [Flags] 2 [Guid] 3127143340 [EditorId] 3127143340 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-34E5P9N) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [48212] Host joined multi-casting on [***********:54997]...
Player connection [48212] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 7
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 2.31 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.0.47f1 (2ad1ed33fd3b)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path C:/Users/<USER>/ONU/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce GTX 1650 (ID=0x1f9d)
    Vendor:   NVIDIA
    VRAM:     3937 MB
    Driver:   32.0.15.7602
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56052
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Registered in 0.002155 seconds.
- Loaded All Assemblies, in  0.325 seconds
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 214 ms
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.514 seconds
Domain Reload Profiling: 838ms
	BeginReloadAssembly (112ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (32ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (38ms)
	LoadAllAssembliesAndSetupDomain (132ms)
		LoadAssemblies (109ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (129ms)
			TypeCache.Refresh (127ms)
				TypeCache.ScanAssembly (117ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (0ms)
	FinalizeReload (515ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (474ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (285ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (41ms)
			ProcessInitializeOnLoadAttributes (94ms)
			ProcessInitializeOnLoadMethodAttributes (50ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.793 seconds
Refreshing native plugins compatible for Editor in 1.80 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation. ---> System.NullReferenceException: Object reference not set to an instance of an object
  at Unity.Multiplayer.Playmode.Workflow.Editor.ProjectDataStore..ctor (System.String directory) [0x0006e] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Configuration\ProjectDataStore.cs:55 
  at Unity.Multiplayer.Playmode.Workflow.Editor.ProjectDataStore.GetMain () [0x00000] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Configuration\ProjectDataStore.cs:36 
  at Unity.Multiplayer.Playmode.Workflow.Editor.WorkflowMainEditorContext..ctor (Unity.Multiplayer.Playmode.VirtualProjects.Editor.MainEditorContext mainEditorContext) [0x0002c] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Contexts\WorkflowMainEditorContext.cs:15 
  at Unity.Multiplayer.Playmode.Workflow.Editor.VirtualProjectWorkflow.InitializeMPPMContexts () [0x00018] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Workflow\VirtualProjectWorkflow.cs:198 
  at Unity.Multiplayer.Playmode.Workflow.Editor.VirtualProjectWorkflow+<>c.<.cctor>b__22_0 () [0x00036] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Workflow\VirtualProjectWorkflow.cs:85 
  at Unity.Multiplayer.Playmode.VirtualProjects.Editor.EditorContexts.SendReadyEvent () [0x00038] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\VirtualProjects\Editor\Contexts\EditorContexts.cs:56 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <********************************>:0 
   --- End of inner exception stack trace ---
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x00083] in <********************************>:0 
  at System.Reflection.MethodBase.Invoke (System.Object obj, System.Object[] parameters) [0x00000] in <********************************>:0 
  at UnityEditor.EditorAssemblies.ProcessInitializeOnLoadMethodAttributes () [0x00056] in <f8e1f14ef858402482f58cef2e987b5b>:0 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

Mirror | mirror-networking.com | discord.gg/N9QVxbM
Mono: successfully reloaded assembly
Package Manager log level set to [2]
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  0.823 seconds
Domain Reload Profiling: 1613ms
	BeginReloadAssembly (142ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (27ms)
	RebuildCommonClasses (30ms)
	RebuildNativeTypeToScriptingClass (13ms)
	initialDomainReloadingComplete (34ms)
	LoadAllAssembliesAndSetupDomain (571ms)
		LoadAssemblies (364ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (290ms)
			TypeCache.Refresh (219ms)
				TypeCache.ScanAssembly (200ms)
			BuildScriptInfoCaches (54ms)
			ResolveRequiredComponents (13ms)
	FinalizeReload (823ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (674ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (10ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (99ms)
			ProcessInitializeOnLoadAttributes (357ms)
			ProcessInitializeOnLoadMethodAttributes (199ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.01 seconds
Refreshing native plugins compatible for Editor in 2.97 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 219 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7357 unused Assets / (7.2 MB). Loaded Objects now: 8019.
Memory consumption went from 185.1 MB to 177.9 MB.
Total: 14.360300 ms (FindLiveObjects: 0.711600 ms CreateObjectMapping: 1.006600 ms MarkObjects: 7.533000 ms  DeleteObjects: 5.105500 ms)

========================================================================
Received Import Request.
  Time since last request: 1841368.932035 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Rug_04C.prefab
  artifactKey: Guid(f7113934a05059543975891f56c0eb97) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Rug_04C.prefab using Guid(f7113934a05059543975891f56c0eb97) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '2c7e404a784c4421f9e084dafba22aa6') in 0.5718412 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Rug_06A.prefab
  artifactKey: Guid(b7813676c823c014388bca3ba0b6ada4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Rug_06A.prefab using Guid(b7813676c823c014388bca3ba0b6ada4) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd1cf4e8abf7cf9148d5d02bec132d275') in 0.0429509 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Rug_05A.prefab
  artifactKey: Guid(b0d747380b605464192d7d8a245309f6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Rug_05A.prefab using Guid(b0d747380b605464192d7d8a245309f6) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '647b7808c67ea90dbd1b82f8b66515f6') in 0.0367791 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Rug_04D.prefab
  artifactKey: Guid(a71dad2a34603ce4ba9f355201eee8a3) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Rug_04D.prefab using Guid(a71dad2a34603ce4ba9f355201eee8a3) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'aebaeb74afbff0a475fee202cfabaf08') in 0.0369305 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.190903 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Rug_06C.prefab
  artifactKey: Guid(6aa87cf017454544e981b5fb0971a4b5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Rug_06C.prefab using Guid(6aa87cf017454544e981b5fb0971a4b5) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0df86bc43083f7b658192dde4504c98d') in 0.0417386 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000019 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Shelf_Box_01C.prefab
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Shelf_Box_01C.prefab using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '6e3276a3eb64a22f0a4610e4b637727e') in 0.0537232 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000020 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Shelves_03C.prefab
  artifactKey: Guid(782b3b927e3633446b1a0c47dd46b5eb) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Shelves_03C.prefab using Guid(782b3b927e3633446b1a0c47dd46b5eb) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'da4d740ec624abf1829de387a9b93459') in 0.0349419 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Standing_Lamp_01A.prefab
  artifactKey: Guid(28f330532e22064438a11c101dd3fb15) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Standing_Lamp_01A.prefab using Guid(28f330532e22064438a11c101dd3fb15) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'bed052f334023a690146a37b415c8cd2') in 0.0554941 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 19

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Shelves_01A.prefab
  artifactKey: Guid(2bdeebb1e34509048b1618f2f36b669f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Shelves_01A.prefab using Guid(2bdeebb1e34509048b1618f2f36b669f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'bddff9472d61525a8ef586560c319b0d') in 0.0350455 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Shelves_03A.prefab
  artifactKey: Guid(6b093c380c43f8f409b9965f9a06a2e2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Shelves_03A.prefab using Guid(6b093c380c43f8f409b9965f9a06a2e2) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd737eaf01437dcf6609e75aab6145459') in 0.0347261 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Shelf_Box_02A.prefab
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Shelf_Box_02A.prefab using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '175dadd9e8fff3723eb08371f90731df') in 0.0326992 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Shelves_02B.prefab
  artifactKey: Guid(31b2ed7ed6bc546459d2ac9054af967e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Shelves_02B.prefab using Guid(31b2ed7ed6bc546459d2ac9054af967e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'a193161b563caf7bdd712363426102ba') in 0.0375211 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.490284 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Standing_Lamp_02A.prefab
  artifactKey: Guid(7ad05c0f24910a64ea17095c8ff3300c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Standing_Lamp_02A.prefab using Guid(7ad05c0f24910a64ea17095c8ff3300c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '3e2f5e93aa8619d70bff4f26b1c02a3d') in 0.0315991 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 19

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Table_01B.prefab
  artifactKey: Guid(143609241ceed384bb182fe9ee86bfba) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Table_01B.prefab using Guid(143609241ceed384bb182fe9ee86bfba) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '841f60d6d96d54ab8fa32b937e6ee531') in 0.0345372 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Standing_Mirror_01B.prefab
  artifactKey: Guid(506d6c97a2702ec4b9981efa715ec355) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Standing_Mirror_01B.prefab using Guid(506d6c97a2702ec4b9981efa715ec355) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '28006511531674b1c121b9fc9297f2a9') in 0.0341417 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 19

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Table_01A.prefab
  artifactKey: Guid(c9272bef6bfd24248bef0d227adbdc7e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Table_01A.prefab using Guid(c9272bef6bfd24248bef0d227adbdc7e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd886559a3e33fa54c0be99d36c30edcc') in 0.0345381 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 14.002205 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_TV_Remote_01A.prefab
  artifactKey: Guid(e0f152b69e23ce14c9bc7be7f98578ec) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_TV_Remote_01A.prefab using Guid(e0f152b69e23ce14c9bc7be7f98578ec) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '54a0244ea2e8df619e1e12c672d93fb7') in 0.0519983 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_TV_Stand_02B.prefab
  artifactKey: Guid(156acaabef040e540bdad18be44d0703) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_TV_Stand_02B.prefab using Guid(156acaabef040e540bdad18be44d0703) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '396b0956a3f02a7fce9f50934e4da6fd') in 0.0337125 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_TV_Stand_02C.prefab
  artifactKey: Guid(bc458ca80206e2f47a35c4bd18d32763) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_TV_Stand_02C.prefab using Guid(bc458ca80206e2f47a35c4bd18d32763) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b74781a8e4a909118014b6f313c0ade1') in 0.0540099 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_TV_Wall_01A.prefab
  artifactKey: Guid(8ddcc0ebdde0a5c47a364a51bd7d6853) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_TV_Wall_01A.prefab using Guid(8ddcc0ebdde0a5c47a364a51bd7d6853) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e42f212dd7d8e88deba0f068a199b86c') in 0.0515169 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0