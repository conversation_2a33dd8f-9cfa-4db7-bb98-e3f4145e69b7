using UnityEngine;


[System.Serializable]
public class BodyType
{
    public Mesh[] meshes;
}


[System.Serializable]
public class AccessoryType
{
    public Mesh[] Helmets;
    public Mesh[] Glasses;
    public Mesh[] Jackets;
}

public class Custumizer : MonoBehaviour
{
    private int selection = 0;
    public CustomPreset skin;
    public BodyType[] body;
    public BodyType[] hair;
    public BodyType[] shirt;
    public BodyType[] pants;
    public BodyType[] shoes;
    public AccessoryType[] accessory;
    private Transform prefab;
    void Start()
    {
        prefab = transform.GetChild(0);
        if (skin == null)
        {
            skin = new CustomPreset();
        }

        Apply();
    }


    public void Apply()
    {
        if (prefab == null)
            prefab = transform.GetChild(0);
        int gender = (int)(skin.body / 3);
        prefab.GetChild(1).GetComponent<SkinnedMeshRenderer>().sharedMesh = body[skin.body].meshes[skin.bodyColor];
        prefab.GetChild(2).GetComponent<SkinnedMeshRenderer>().sharedMesh = hair[gender].meshes[skin.hair];
        prefab.GetChild(3).GetComponent<SkinnedMeshRenderer>().sharedMesh = shirt[gender].meshes[skin.shirt];
        prefab.GetChild(4).GetComponent<SkinnedMeshRenderer>().sharedMesh = pants[gender].meshes[skin.pants];
        prefab.GetChild(5).GetComponent<SkinnedMeshRenderer>().sharedMesh = shoes[gender].meshes[skin.shoes];
        if (skin.helmet != -1)
        {
            prefab.GetChild(6).GetComponent<SkinnedMeshRenderer>().sharedMesh = accessory[gender].Helmets[skin.helmet];
        }
        else
        {
            prefab.GetChild(6).GetComponent<SkinnedMeshRenderer>().sharedMesh = null;
        }
        if (skin.glasses != -1)
        {
            prefab.GetChild(7).GetComponent<SkinnedMeshRenderer>().sharedMesh = accessory[gender].Glasses[skin.glasses];
        }
        else
        {
            prefab.GetChild(7).GetComponent<SkinnedMeshRenderer>().sharedMesh = null;
        }
        if (skin.jacket != -1)
        {
            prefab.GetChild(8).GetComponent<SkinnedMeshRenderer>().sharedMesh = accessory[gender].Jackets[skin.jacket];
        }
        else
        {
            prefab.GetChild(8).GetComponent<SkinnedMeshRenderer>().sharedMesh = null;
        }
    }

    public void Select(int a)
    {
        selection = a;
    }
    public void Change()
    {
        switch (selection)
        {
            case 0:
                skin.body++;
                if (skin.body > 5)
                {
                    skin.body = 0;
                }
                break;
            case 1:
                skin.bodyColor++;
                if (skin.bodyColor > 2)
                {
                    skin.bodyColor = 0;
                }
                break;
            case 2:
                skin.hair++;
                if (skin.hair > 8)
                {
                    skin.hair = 0;
                }
                break;  
            case 3:
                skin.shirt++;
                if (skin.shirt > 8)
                {
                    skin.shirt = 0;
                }
                break;  
            case 4:
                skin.pants++;
                if (skin.pants > 8)
                {
                    skin.pants = 0;
                }
                break;  

}

[System.Serializable]
public class CustomPreset
{
    [Range(0, 5)]
    public int body;
    [Range(0, 2)]
    public int bodyColor;
    [Range(0, 8)]
    public int hair;
    [Range(0, 8)]
    public int shirt;
    [Range(0, 8)]
    public int pants;
    [Range(0, 8)]
    public int shoes;
    [Range(-1, 2)]
    public int helmet;
    [Range(-1, 3)]
    public int glasses;
    [Range(-1, 2)]
    public int jacket;

    public CustomPreset(int body, int bodyColor, int hair, int shirt, int pants, int shoes, int helmet, int glasses, int jacket)
    {
        this.body = body;
        this.bodyColor = bodyColor;
        this.hair = hair;
        this.shirt = shirt;
        this.pants = pants;
        this.shoes = shoes;
        this.helmet = helmet;
        this.glasses = glasses;
        this.jacket = jacket;
    }

    public CustomPreset()
    {
        this.body = 0;
        this.bodyColor = 0;
        this.hair = 0;
        this.shirt = 0;
        this.pants = 0;
        this.shoes = 0;
        this.helmet = -1;
        this.glasses = -1;
        this.jacket = -1;
    }
    

}
