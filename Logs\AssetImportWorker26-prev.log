Using pre-set license
Built from '6000.0/staging' branch; Version is '6000.0.47f1 (2ad1ed33fd3b) revision 2806253'; Using compiler version '193933523'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit CoreSingleLanguage' Language: 'fr' Physical Memory: 16051 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker26
-projectPath
C:/Users/<USER>/ONU
-logFile
Logs/AssetImportWorker26.log
-srvPort
56204
-job-worker-count
7
-background-job-worker-count
8
-gc-helper-count
1
Successfully changed project path to: C:/Users/<USER>/ONU
C:/Users/<USER>/ONU
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [10528]  Target information:

Player connection [10528]  * "[IP] ************** [Port] 0 [Flags] 2 [Guid] 2080955526 [EditorId] 2080955526 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-34E5P9N) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [10528] Host joined multi-casting on [***********:54997]...
Player connection [10528] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 7
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 2.57 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.0.47f1 (2ad1ed33fd3b)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path C:/Users/<USER>/ONU/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce GTX 1650 (ID=0x1f9d)
    Vendor:   NVIDIA
    VRAM:     3937 MB
    Driver:   32.0.15.7602
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56000
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Registered in 0.002492 seconds.
- Loaded All Assemblies, in  0.390 seconds
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 217 ms
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.517 seconds
Domain Reload Profiling: 906ms
	BeginReloadAssembly (126ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (35ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (49ms)
	LoadAllAssembliesAndSetupDomain (169ms)
		LoadAssemblies (124ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (165ms)
			TypeCache.Refresh (164ms)
				TypeCache.ScanAssembly (151ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (0ms)
	FinalizeReload (517ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (475ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (301ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (37ms)
			ProcessInitializeOnLoadAttributes (87ms)
			ProcessInitializeOnLoadMethodAttributes (47ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.- Loaded All Assemblies, in  0.647 seconds
Refreshing native plugins compatible for Editor in 1.79 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
Package Manager log level set to [2]
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  0.595 seconds
Domain Reload Profiling: 1239ms
	BeginReloadAssembly (128ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (22ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (33ms)
	LoadAllAssembliesAndSetupDomain (445ms)
		LoadAssemblies (306ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (215ms)
			TypeCache.Refresh (157ms)
				TypeCache.ScanAssembly (143ms)
			BuildScriptInfoCaches (45ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (595ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (465ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (9ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (86ms)
			ProcessInitializeOnLoadAttributes (297ms)
			ProcessInitializeOnLoadMethodAttributes (66ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.01 seconds
Refreshing native plugins compatible for Editor in 2.82 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 200 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6553 unused Assets / (7.6 MB). Loaded Objects now: 7198.
Memory consumption went from 171.3 MB to 163.7 MB.
Total: 13.091100 ms (FindLiveObjects: 0.623200 ms CreateObjectMapping: 0.769200 ms MarkObjects: 6.341600 ms  DeleteObjects: 5.353900 ms)

========================================================================
Received Import Request.
  Time since last request: 1488940.037329 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Kitchen_Counter_01B.fbx
  artifactKey: Guid(ae9211dfad017cd41a7e86f702b08583) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Kitchen_Counter_01B.fbx using Guid(ae9211dfad017cd41a7e86f702b08583) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '29f8c386b1b9f2a60ec92693560268e2') in 0.5111 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Lounge_Chair_01A.fbx
  artifactKey: Guid(3f6a11680ea35d043a71b7335fdaef43) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Lounge_Chair_01A.fbx using Guid(3f6a11680ea35d043a71b7335fdaef43) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'fcc17b49da48f50620c5c192799d1327') in 0.0270379 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Mailbox_02A.fbx
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Mailbox_02A.fbx using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '2d3cd804ad961bc97801aa499531b22f') in 0.0236764 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Kitchen_Shelf_01A.fbx
  artifactKey: Guid(69e86287f09e2d7449778ebcff40a9c1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Kitchen_Shelf_01A.fbx using Guid(69e86287f09e2d7449778ebcff40a9c1) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '679cee287028fff9a0817947bafda930') in 0.026639 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Nightstand_01A.fbx
  artifactKey: Guid(b6ee6632a76aac54f993f8247b47ada6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Nightstand_01A.fbx using Guid(b6ee6632a76aac54f993f8247b47ada6) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '37213d7e681d9a92926d30b2504acbfc') in 0.0265883 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Light_Switch_01A.fbx
  artifactKey: Guid(fcc5682fda1ed4b4f881cfa68de12a47) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Light_Switch_01A.fbx using Guid(fcc5682fda1ed4b4f881cfa68de12a47) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '7a8463e2833d56502e5273d57cbf9e22') in 0.0265154 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorkerClient::OnTransportError - code=2 error=End of file
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0