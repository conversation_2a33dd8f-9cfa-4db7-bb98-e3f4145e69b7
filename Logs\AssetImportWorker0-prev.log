Using pre-set license
Built from '6000.0/staging' branch; Version is '6000.0.47f1 (2ad1ed33fd3b) revision 2806253'; Using compiler version '193933523'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit CoreSingleLanguage' Language: 'fr' Physical Memory: 16051 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker0
-projectPath
C:/Users/<USER>/ONU
-logFile
Logs/AssetImportWorker0.log
-srvPort
56204
-job-worker-count
7
-background-job-worker-count
8
-gc-helper-count
1
Successfully changed project path to: C:/Users/<USER>/ONU
C:/Users/<USER>/ONU
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [12368]  Target information:

Player connection [12368]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 2967234999 [EditorId] 2967234999 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-34E5P9N) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [12368] Host joined multi-casting on [***********:54997]...
Player connection [12368] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 7
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 6.02 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.0.47f1 (2ad1ed33fd3b)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path C:/Users/<USER>/ONU/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce GTX 1650 (ID=0x1f9d)
    Vendor:   NVIDIA
    VRAM:     3937 MB
    Driver:   32.0.15.7602
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56292
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Registered in 0.004340 seconds.
- Loaded All Assemblies, in  0.701 seconds
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 1403 ms
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.114 seconds
Domain Reload Profiling: 2814ms
	BeginReloadAssembly (206ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (41ms)
	RebuildNativeTypeToScriptingClass (13ms)
	initialDomainReloadingComplete (60ms)
	LoadAllAssembliesAndSetupDomain (379ms)
		LoadAssemblies (204ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (374ms)
			TypeCache.Refresh (371ms)
				TypeCache.ScanAssembly (346ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (2115ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (2026ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (1573ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (70ms)
			ProcessInitializeOnLoadAttributes (255ms)
			ProcessInitializeOnLoadMethodAttributes (124ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.032 seconds
Refreshing native plugins compatible for Editor in 12.72 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
Package Manager log level set to [2]
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  3.345 seconds
Domain Reload Profiling: 4375ms
	BeginReloadAssembly (279ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (49ms)
	RebuildCommonClasses (40ms)
	RebuildNativeTypeToScriptingClass (14ms)
	initialDomainReloadingComplete (42ms)
	LoadAllAssembliesAndSetupDomain (653ms)
		LoadAssemblies (510ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (301ms)
			TypeCache.Refresh (193ms)
				TypeCache.ScanAssembly (172ms)
			BuildScriptInfoCaches (60ms)
			ResolveRequiredComponents (39ms)
	FinalizeReload (3346ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (2625ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (52ms)
			SetLoadedEditorAssemblies (17ms)
			BeforeProcessingInitializeOnLoad (402ms)
			ProcessInitializeOnLoadAttributes (1682ms)
			ProcessInitializeOnLoadMethodAttributes (454ms)
			AfterProcessingInitializeOnLoad (18ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (25ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.03 seconds
Refreshing native plugins compatible for Editor in 11.08 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 191 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6182 unused Assets / (6.8 MB). Loaded Objects now: 6812.
Memory consumption went from 154.0 MB to 147.3 MB.
Total: 28.608100 ms (FindLiveObjects: 2.450500 ms CreateObjectMapping: 2.127000 ms MarkObjects: 14.273600 ms  DeleteObjects: 9.749800 ms)

========================================================================
Received Import Request.
  Time since last request: 1471224.786161 seconds.
  path: Assets/InputSystem_Actions.inputactions
  artifactKey: Guid(052faaac586de48259a63d0c4782560b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/InputSystem_Actions.inputactions using Guid(052faaac586de48259a63d0c4782560b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '7dfe44373263f7024e54e78e91739bdf') in 0.1019449 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 20

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  3.086 seconds
Refreshing native plugins compatible for Editor in 12.68 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  2.992 seconds
Domain Reload Profiling: 6069ms
	BeginReloadAssembly (664ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (23ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (146ms)
	RebuildCommonClasses (129ms)
	RebuildNativeTypeToScriptingClass (53ms)
	initialDomainReloadingComplete (107ms)
	LoadAllAssembliesAndSetupDomain (2122ms)
		LoadAssemblies (1221ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (1211ms)
			TypeCache.Refresh (772ms)
				TypeCache.ScanAssembly (722ms)
			BuildScriptInfoCaches (381ms)
			ResolveRequiredComponents (45ms)
	FinalizeReload (2993ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (2136ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (51ms)
			SetLoadedEditorAssemblies (19ms)
			BeforeProcessingInitializeOnLoad (455ms)
			ProcessInitializeOnLoadAttributes (1366ms)
			ProcessInitializeOnLoadMethodAttributes (225ms)
			AfterProcessingInitializeOnLoad (21ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (63ms)
Refreshing native plugins compatible for Editor in 14.28 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 43 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6189 unused Assets / (7.4 MB). Loaded Objects now: 6841.
Memory consumption went from 147.8 MB to 140.5 MB.
Total: 35.033700 ms (FindLiveObjects: 1.760400 ms CreateObjectMapping: 1.795700 ms MarkObjects: 19.993200 ms  DeleteObjects: 11.481800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 9941.921470 seconds.
  path: Assets/Bonsai_Big.prefab
  artifactKey: Guid(be149cf1c62e8084cac28e2f905d9267) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Bonsai_Big.prefab using Guid(be149cf1c62e8084cac28e2f905d9267) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '046e15d49324257e95e8d3194115dffe') in 1.3743866 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 29.751378 seconds.
  path: Assets/Bonsai_Big 1.prefab
  artifactKey: Guid(8ac31624db29d3749b28a4a8fd968d23) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Bonsai_Big 1.prefab using Guid(8ac31624db29d3749b28a4a8fd968d23) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '48d280defb2a3ddc388a7097da9cc263') in 0.1966797 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 20.799726 seconds.
  path: Assets/Bonsai_Big.fbx
  artifactKey: Guid(7bae059ac81b34f4085698066db4502f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Bonsai_Big.fbx using Guid(7bae059ac81b34f4085698066db4502f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '782bf15f6807ce5d2f7d23e7ff1024f3') in 4.7243216 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 37.662100 seconds.
  path: Assets/Textures
  artifactKey: Guid(aace2ae018a995848b4227815e9fbaaf) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Textures using Guid(aace2ae018a995848b4227815e9fbaaf) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '1889c43ea4abccd5327b3bca2890813f') in 0.0034861 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.901515 seconds.
  path: Assets/Textures/Bark
  artifactKey: Guid(b42b0d8234e726145bb6884fc294f42f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Textures/Bark using Guid(b42b0d8234e726145bb6884fc294f42f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '66c1dfb312cd2278c1c6811b582b521f') in 0.0025984 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Textures/Bark/Moss.PNG
  artifactKey: Guid(0c1d5e2517435234787a838cfa7dbcaf) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Textures/Bark/Moss.PNG using Guid(0c1d5e2517435234787a838cfa7dbcaf) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '9c3e2cb4c20ef0419e4c8fce8fee9e76') in 0.053708 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/Textures/Bark/Wood_5_Normal.png
  artifactKey: Guid(2c67adbdc567faf47a710853a4477b13) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Textures/Bark/Wood_5_Normal.png using Guid(2c67adbdc567faf47a710853a4477b13) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'a3a65d0830d95c5a182526062af3dce9') in 0.0323292 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Textures/Bark/Wood_5.png
  artifactKey: Guid(954eb3f8e265cf5409a1bcfc03c887af) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Textures/Bark/Wood_5.png using Guid(954eb3f8e265cf5409a1bcfc03c887af) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ff8e09032a5ced26e49cc393b551488d') in 0.0228544 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 16.026367 seconds.
  path: Assets
  artifactKey: Guid(00000000000000001000000000000000) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets using Guid(00000000000000001000000000000000) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c391582d739b6e62a5220f35cbb167ac') in 0.0012563 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 4.611776 seconds.
  path: Assets/Tree
  artifactKey: Guid(ba77ae5851d50d543a28d2bf96b490c1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Tree using Guid(ba77ae5851d50d543a28d2bf96b490c1) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c5399b2a3d8902b1cd7f0a1fc2b9b050') in 0.0019235 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 14.130230 seconds.
  path: Assets/Tree/Materials
  artifactKey: Guid(93a45b8ce5976b5419cd1e567f166f76) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Tree/Materials using Guid(93a45b8ce5976b5419cd1e567f166f76) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '46a449f3bedd56338923aed3fe7ccce8') in 0.0006345 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 1.788050 seconds.
  path: Assets/Tree/Materials/URP
  artifactKey: Guid(064186ee54afa934d872b55e01cc993e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Tree/Materials/URP using Guid(064186ee54afa934d872b55e01cc993e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e92b6835b3937c9311faec9a50f31b5c') in 0.0005215 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.850285 seconds.
  path: Assets/Tree/Materials/URP/Bark
  artifactKey: Guid(09ed75bbdc9d30849a7bff89418ecfa8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Tree/Materials/URP/Bark using Guid(09ed75bbdc9d30849a7bff89418ecfa8) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e1552d9848dbcc892010e4074ec84796') in 0.0006099 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.023529 seconds.
  path: Assets/Tree/Materials/URP/Bark/Wood_5.mat
  artifactKey: Guid(dcac3cbc83ad57c498a27727d59cb3d5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Tree/Materials/URP/Bark/Wood_5.mat using Guid(dcac3cbc83ad57c498a27727d59cb3d5) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'fed9a06ed5aaf42fbf17c29530947d20') in 0.0263661 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 5

========================================================================
Received Import Request.
  Time since last request: 20.386756 seconds.
  path: Assets/Trees
  artifactKey: Guid(d046392f5a747614b95c9d6f36c7c3af) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Trees using Guid(d046392f5a747614b95c9d6f36c7c3af) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '34bf5a11b972d4de72bfa714fb0c4193') in 0.0012778 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 2.574143 seconds.
  path: Assets/Trees/ManaTreeAnimation.anim
  artifactKey: Guid(6c3ebf9a776ba6849b770583d51c3105) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Trees/ManaTreeAnimation.anim using Guid(6c3ebf9a776ba6849b770583d51c3105) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '3b146e03b6d9bf73fbe92fdea0868bbf') in 0.0016072 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 2.324273 seconds.
  path: Assets/Trees/Tree.controller
  artifactKey: Guid(bf85f1b508e790446bca4ad9d7ff6a79) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Trees/Tree.controller using Guid(bf85f1b508e790446bca4ad9d7ff6a79) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '67a09ce51012cb73749a0a8b5ee148ca') in 0.005293 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 2.372539 seconds.
  path: Assets/Trees/Prefabs
  artifactKey: Guid(b00d828f215880b43906d27d32b58715) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Trees/Prefabs using Guid(b00d828f215880b43906d27d32b58715) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '00302dfedff4e41fd3acd3f5db9a502b') in 0.0004965 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.742251 seconds.
  path: Assets/Trees/Prefabs/URP
  artifactKey: Guid(6e97789a8dc255140ad74e1f2a7cf62e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Trees/Prefabs/URP using Guid(6e97789a8dc255140ad74e1f2a7cf62e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '24cec85872ea2479b9f181be09803bfa') in 0.0044172 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.044979 seconds.
  path: Assets/Trees/Prefabs/URP/BigTree.prefab
  artifactKey: Guid(90d0236f9b7037a4c98e9f02a79e2bee) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Trees/Prefabs/URP/BigTree.prefab using Guid(90d0236f9b7037a4c98e9f02a79e2bee) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'bff1aab81ba823d09011918e31b68b3b') in 1.3849642 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.120914 seconds.
  path: Assets/Trees/Source/Materials/URP/Falling_Leaves/Bambo.mat
  artifactKey: Guid(92e39ba4c6322b743898a08eb9129e37) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Trees/Source/Materials/URP/Falling_Leaves/Bambo.mat using Guid(92e39ba4c6322b743898a08eb9129e37) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '75d8dd0d3fab9ff91f5a760d4291a425') in 0.0238439 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 56.385406 seconds.
  path: Assets/Trees/Source
  artifactKey: Guid(14dbf199a26943c438c841962336108d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Trees/Source using Guid(14dbf199a26943c438c841962336108d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '9eee19c4bdbdfb3b5e8547f1b6106189') in 0.0007619 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.694718 seconds.
  path: Assets/Trees/Source/Materials
  artifactKey: Guid(94e20e36a7361b44898f676129117004) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Trees/Source/Materials using Guid(94e20e36a7361b44898f676129117004) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e7895ad856ceccb31f6a6534f04dd2cc') in 0.0009851 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.682264 seconds.
  path: Assets/Trees/Source/Models
  artifactKey: Guid(a78ea9930db1e2c49abee67585d7739b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Trees/Source/Models using Guid(a78ea9930db1e2c49abee67585d7739b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '400fd1e62046289fc46135e145379788') in 0.0013346 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.040879 seconds.
  path: Assets/Trees/Source/Models/Bonsai_Big.fbx
  artifactKey: Guid(8d3c421ebb7fcae43bc04164ad41bf93) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Trees/Source/Models/Bonsai_Big.fbx using Guid(8d3c421ebb7fcae43bc04164ad41bf93) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'fc072dc25a227eaddb4953cd93bd0ea4') in 0.0626092 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 14

========================================================================
Received Import Request.
  Time since last request: 10.146914 seconds.
  path: Assets/Trees/Source/Shaders
  artifactKey: Guid(59eb7954de668164daf8290fe3f5b118) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Trees/Source/Shaders using Guid(59eb7954de668164daf8290fe3f5b118) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e4394fef1851da632fb1a9733247970d') in 0.0007371 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 1.592840 seconds.
  path: Assets/Trees/Source/Textures
  artifactKey: Guid(db29c726b3f59e1429cd7a3eab5d7f46) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Trees/Source/Textures using Guid(db29c726b3f59e1429cd7a3eab5d7f46) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'aa296d350bd9ff517bedd578c93faf13') in 0.0005815 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 15.490277 seconds.
  path: Assets/Trees/Source/Materials/URP/Falling_Leaves
  artifactKey: Guid(65aa60134718c4b44a8c79777da15f0b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Trees/Source/Materials/URP/Falling_Leaves using Guid(65aa60134718c4b44a8c79777da15f0b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b9d60973bc80d1ee1d14a72b8f872127') in 0.0005837 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.049535 seconds.
  path: Assets/Trees/Source/Materials/URP/Falling_Leaves/Blossom.mat
  artifactKey: Guid(15812e58096b11247a1d869fee1102c2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Trees/Source/Materials/URP/Falling_Leaves/Blossom.mat using Guid(15812e58096b11247a1d869fee1102c2) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0fdef7075000cf9a44364f79db4108d8') in 0.035007 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 34.726483 seconds.
  path: Assets/Trees/Source/Shaders/URP
  artifactKey: Guid(6f43e2504df9d8047851d0ec003dbda9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Trees/Source/Shaders/URP using Guid(6f43e2504df9d8047851d0ec003dbda9) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e8820bdece233490bc308b564c683860') in 0.0007069 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.867112 seconds.
  path: Assets/Trees/Source/Shaders/URP/Leaves
  artifactKey: Guid(d57df7346dbd5ea448c0da37b0520608) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Trees/Source/Shaders/URP/Leaves using Guid(d57df7346dbd5ea448c0da37b0520608) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0a43627bb4ddf831d05b3b79e8883a87') in 0.0005729 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 11.813565 seconds.
  path: Assets/Trees/Source/Textures/Leaves
  artifactKey: Guid(4a5cef0cdb965b84c9b18843374360e6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Trees/Source/Textures/Leaves using Guid(4a5cef0cdb965b84c9b18843374360e6) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f37148a9c6de15f0d8831ee4f59bfa67') in 0.0005957 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.020865 seconds.
  path: Assets/Trees/Source/Textures/Leaves/Bambo_Leaf.png
  artifactKey: Guid(11134ad5c3a640e4493b48b96033632f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Trees/Source/Textures/Leaves/Bambo_Leaf.png using Guid(11134ad5c3a640e4493b48b96033632f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b3f3d8354dbd4cc934774ee284c1ac2f') in 0.0259765 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Trees/Source/Textures/Leaves/Bushy_Leaf.png
  artifactKey: Guid(4007dbadd066ea747a86b1f097c9b9da) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Trees/Source/Textures/Leaves/Bushy_Leaf.png using Guid(4007dbadd066ea747a86b1f097c9b9da) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '4a5d6801036c39532cbc7e9eeb92bc01') in 0.0228344 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/Trees/Source/Textures/Leaves/Blossom.png
  artifactKey: Guid(012b51afc0046d0438b6f8810ef91daa) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Trees/Source/Textures/Leaves/Blossom.png using Guid(012b51afc0046d0438b6f8810ef91daa) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '6620cf367e66771a1ab94ab01c8ca588') in 0.0223994 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Trees/Source/Textures/Leaves/Blossom_Branch_Normal.png
  artifactKey: Guid(892db7f031af0be4988e2a1be2b47b1a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Trees/Source/Textures/Leaves/Blossom_Branch_Normal.png using Guid(892db7f031af0be4988e2a1be2b47b1a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '6230104e47a1edde48d0fcefc9115251') in 0.020985 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0