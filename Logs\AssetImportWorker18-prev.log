Using pre-set license
Built from '6000.0/staging' branch; Version is '6000.0.47f1 (2ad1ed33fd3b) revision 2806253'; Using compiler version '193933523'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit CoreSingleLanguage' Language: 'fr' Physical Memory: 16051 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker18
-projectPath
C:/Users/<USER>/ONU
-logFile
Logs/AssetImportWorker18.log
-srvPort
56204
-job-worker-count
7
-background-job-worker-count
8
-gc-helper-count
1
Successfully changed project path to: C:/Users/<USER>/ONU
C:/Users/<USER>/ONU
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [19428]  Target information:

Player connection [19428]  * "[IP] ************** [Port] 0 [Flags] 2 [Guid] 3679222712 [EditorId] 3679222712 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-34E5P9N) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [19428] Host joined multi-casting on [***********:54997]...
Player connection [19428] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 7
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 296.10 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.0.47f1 (2ad1ed33fd3b)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path C:/Users/<USER>/ONU/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce GTX 1650 (ID=0x1f9d)
    Vendor:   NVIDIA
    VRAM:     3937 MB
    Driver:   32.0.15.7602
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56652
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Registered in 1.688192 seconds.
- Loaded All Assemblies, in 23.207 seconds
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 309 ms
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.721 seconds
Domain Reload Profiling: 23927ms
	BeginReloadAssembly (20222ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (51ms)
	RebuildNativeTypeToScriptingClass (18ms)
	initialDomainReloadingComplete (1742ms)
	LoadAllAssembliesAndSetupDomain (1173ms)
		LoadAssemblies (20248ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (1143ms)
			TypeCache.Refresh (1142ms)
				TypeCache.ScanAssembly (1124ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (0ms)
	FinalizeReload (722ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (681ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (397ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (36ms)
			ProcessInitializeOnLoadAttributes (152ms)
			ProcessInitializeOnLoadMethodAttributes (93ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in 28.552 seconds
Refreshing native plugins compatible for Editor in 5.31 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
Package Manager log level set to [2]
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  2.964 seconds
Domain Reload Profiling: 31516ms
	BeginReloadAssembly (129ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (23ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (29ms)
	LoadAllAssembliesAndSetupDomain (28355ms)
		LoadAssemblies (27815ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (613ms)
			TypeCache.Refresh (447ms)
				TypeCache.ScanAssembly (396ms)
			BuildScriptInfoCaches (135ms)
			ResolveRequiredComponents (20ms)
	FinalizeReload (2966ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1607ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (27ms)
			SetLoadedEditorAssemblies (10ms)
			BeforeProcessingInitializeOnLoad (266ms)
			ProcessInitializeOnLoadAttributes (749ms)
			ProcessInitializeOnLoadMethodAttributes (548ms)
			AfterProcessingInitializeOnLoad (8ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (13ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.03 seconds
Refreshing native plugins compatible for Editor in 3.15 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 198 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6205 unused Assets / (6.9 MB). Loaded Objects now: 6851.
Memory consumption went from 162.9 MB to 155.9 MB.
Total: 14.733700 ms (FindLiveObjects: 0.725000 ms CreateObjectMapping: 1.091400 ms MarkObjects: 7.258500 ms  DeleteObjects: 5.654600 ms)

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.145 seconds
Refreshing native plugins compatible for Editor in 2.69 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  0.920 seconds
Domain Reload Profiling: 2067ms
	BeginReloadAssembly (340ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (87ms)
	RebuildCommonClasses (58ms)
	RebuildNativeTypeToScriptingClass (21ms)
	initialDomainReloadingComplete (42ms)
	LoadAllAssembliesAndSetupDomain (685ms)
		LoadAssemblies (576ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (280ms)
			TypeCache.Refresh (13ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (252ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (921ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (646ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (19ms)
			SetLoadedEditorAssemblies (10ms)
			BeforeProcessingInitializeOnLoad (146ms)
			ProcessInitializeOnLoadAttributes (423ms)
			ProcessInitializeOnLoadMethodAttributes (45ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 3.53 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 42 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6203 unused Assets / (7.5 MB). Loaded Objects now: 6866.
Memory consumption went from 158.1 MB to 150.5 MB.
Total: 16.420500 ms (FindLiveObjects: 1.375800 ms CreateObjectMapping: 1.027900 ms MarkObjects: 7.069000 ms  DeleteObjects: 6.942000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 1483216.381562 seconds.
  path: Assets/Readme.asset
  artifactKey: Guid(8105016687592461f977c054a80ce2f2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Readme.asset using Guid(8105016687592461f977c054a80ce2f2) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ec25fa67f6be1dbd5133477a62e90cde') in 0.0323882 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 3.018457 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Antenna_01A.fbx
  artifactKey: Guid(58bbc7f26709bd845a557fbc03ef81e2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Antenna_01A.fbx using Guid(58bbc7f26709bd845a557fbc03ef81e2) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd71dfde81f006eb7f9ea87bd1abbdd0b') in 3.1346449 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 17

========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Couch_01A.fbx
  artifactKey: Guid(8365672fec6004e4395ce6fc4d808a6a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Couch_01A.fbx using Guid(8365672fec6004e4395ce6fc4d808a6a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c27ec74db5fc99a6f6721d4234cbaaae') in 0.0437675 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Bathroom_Wall_Mirror_03B.fbx
  artifactKey: Guid(bb548dea44b8f2b4f9e99e3610797c3a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Bathroom_Wall_Mirror_03B.fbx using Guid(bb548dea44b8f2b4f9e99e3610797c3a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c7e60ce5140c09c2934e1176b5029ea5') in 0.0372778 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Armchair_01A.fbx
  artifactKey: Guid(879a2304d95fb9d4a89fec70af74e0d5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Armchair_01A.fbx using Guid(879a2304d95fb9d4a89fec70af74e0d5) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '1b04cb328fdf68c3288adcd2a20a549f') in 0.0419735 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Armchair_02C.fbx
  artifactKey: Guid(def2c2d1974c9754180c25f2589b214d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Armchair_02C.fbx using Guid(def2c2d1974c9754180c25f2589b214d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'a91a226f3c8c58d33b630959dba0f7dc') in 0.0301516 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Bowl_02C.fbx
  artifactKey: Guid(897e8086f60a1da41a4b63e16323ff4f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Bowl_02C.fbx using Guid(897e8086f60a1da41a4b63e16323ff4f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c7f400ca4fdac845abdad3ff1da037cf') in 0.0318873 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Chair_Desk_01A.fbx
  artifactKey: Guid(eb17e6390cb736a4084cf6bde71d3a01) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Chair_Desk_01A.fbx using Guid(eb17e6390cb736a4084cf6bde71d3a01) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'db01f66eb32809977ab0359810bdad76') in 0.0418347 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Bookshelf_02A.fbx
  artifactKey: Guid(17fb36bf9bc60fa4d9e1a20117d79960) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Bookshelf_02A.fbx using Guid(17fb36bf9bc60fa4d9e1a20117d79960) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f51b2824e37ed639f2cef7f79e35d5a8') in 0.0277043 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000065 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Book_02A.fbx
  artifactKey: Guid(54d4459e840ae0149b4409aa135aecb5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Book_02A.fbx using Guid(54d4459e840ae0149b4409aa135aecb5) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '6b8011eb3628ce8003de3b4543c37178') in 0.0249708 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Can_02B.fbx
  artifactKey: Guid(28460a41bd4e8ef45b1994c9aeb0532f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Can_02B.fbx using Guid(28460a41bd4e8ef45b1994c9aeb0532f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '992f46a8003904f9307d2092b7c6ba94') in 0.0335181 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Bathroom_Shower_Cabin_01B.fbx
  artifactKey: Guid(416adae4b687c9f49832d606a0310070) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Bathroom_Shower_Cabin_01B.fbx using Guid(416adae4b687c9f49832d606a0310070) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '51889e62e917dde82174a691163e427b') in 0.0586587 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 23

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Coffee_Mug_01A.fbx
  artifactKey: Guid(ed24ce959741d524b972661cd843c8fb) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Coffee_Mug_01A.fbx using Guid(ed24ce959741d524b972661cd843c8fb) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c0d788eea26ac75e68b2885f37d58a0f') in 0.0295038 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Bush_02C.fbx
  artifactKey: Guid(579ddfd5a534e8f4dba27117434a774d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Bush_02C.fbx using Guid(579ddfd5a534e8f4dba27117434a774d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0b57c83acb06a121ac0f393bf7467a07') in 4.1605992 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 14

========================================================================
Received Import Request.
  Time since last request: 0.000067 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Characters Source/TSP_Male_Character_02.prefab
  artifactKey: Guid(617f9eecc17764a49af2a3304e5259af) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Characters Source/TSP_Male_Character_02.prefab using Guid(617f9eecc17764a49af2a3304e5259af) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '3b375260e756332bd372c4ed281266a3') in 0.2682918 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 164

========================================================================
Received Import Request.
  Time since last request: 0.000087 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Characters Source/TSP_Female_Character_03.prefab
  artifactKey: Guid(c4c4dc12325b4904c971468b74f7fab0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Characters Source/TSP_Female_Character_03.prefab using Guid(c4c4dc12325b4904c971468b74f7fab0) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b539989d342fd296f38a125cfcbe624a') in 0.0576072 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 188

========================================================================
Received Import Request.
  Time since last request: 0.000067 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Characters Source/TSP_Female_Character_01.prefab
  artifactKey: Guid(5b68b68a9d10cfa4aa2518050a3697bb) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Characters Source/TSP_Female_Character_01.prefab using Guid(5b68b68a9d10cfa4aa2518050a3697bb) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e0b2a9d2b3640cf7e4d3e0d3bbbefba5') in 0.0375875 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 178

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Characters Source/TSP_Female_Character_02.prefab
  artifactKey: Guid(4c971d62c44f0af4ba837dcc8dc04f03) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Characters Source/TSP_Female_Character_02.prefab using Guid(4c971d62c44f0af4ba837dcc8dc04f03) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '3fdaa59d5e4dea2d08a1c42048a1069c') in 0.0387113 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 162

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Characters Source/TSP_Male_Character_01.prefab
  artifactKey: Guid(3ce3a137ad97de64782f27b115dbe58f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Characters Source/TSP_Male_Character_01.prefab using Guid(3ce3a137ad97de64782f27b115dbe58f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'aa75e89eca0e7c994424cd478f54cbe9') in 0.044137 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 174

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Bathroom_Wall_Cabinet_01A.fbx
  artifactKey: Guid(0c7a4c18283e46046a0b366c8e5c91a8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Bathroom_Wall_Cabinet_01A.fbx using Guid(0c7a4c18283e46046a0b366c8e5c91a8) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c9dd53b650f876b1d1a79db551853b05') in 0.0472204 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Can_02C.fbx
  artifactKey: Guid(13933af2aa173f147babead8b765b391) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Can_02C.fbx using Guid(13933af2aa173f147babead8b765b391) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '6ea44de9df3f0f1c3a73ad7e72c29285') in 0.036419 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Chandelier_01A.fbx
  artifactKey: Guid(c490c3aa0e391b24c9e54055cd66e91d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Chandelier_01A.fbx using Guid(c490c3aa0e391b24c9e54055cd66e91d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '6cf684bf66f32e0f9b2b69983b5a98a8') in 0.0541137 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 18

========================================================================
Received Import Request.
  Time since last request: 0.000062 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Concrete_Beam_01A.fbx
  artifactKey: Guid(243521150b59dc240bf06e0520758574) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Concrete_Beam_01A.fbx using Guid(243521150b59dc240bf06e0520758574) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '8fda3f4c5a922721dc2d9b4b9d35dea6') in 0.035634 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Bathroom_Wall_Faucet_01B.fbx
  artifactKey: Guid(2bff4abc581b88a4b86cbd8dc1a67006) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Bathroom_Wall_Faucet_01B.fbx using Guid(2bff4abc581b88a4b86cbd8dc1a67006) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'da5e1190a15e7d9cda435fe26ba417b9') in 0.0371212 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Ceiling_Fixture_04A.fbx
  artifactKey: Guid(8d17c06a88796e14f9bbe0b58c0c9726) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Ceiling_Fixture_04A.fbx using Guid(8d17c06a88796e14f9bbe0b58c0c9726) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd4f568877721139fa03018e55aa0d4e1') in 0.0471209 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 18

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Canopy_01A.fbx
  artifactKey: Guid(9e696507a5d7b9e419b9a65ad31c2ecb) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Canopy_01A.fbx using Guid(9e696507a5d7b9e419b9a65ad31c2ecb) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '3105ad7323933b97149610778dfbed90') in 0.0410772 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Bathroom_Wall_Mount_02B.fbx
  artifactKey: Guid(99d49b8e5db340e40947817f7073ed7e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Bathroom_Wall_Mount_02B.fbx using Guid(99d49b8e5db340e40947817f7073ed7e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '5d598c6a8df463d3857ff6dba0453199') in 0.0337216 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000077 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Checkout_02A.fbx
  artifactKey: Guid(8f7e48f6945327b4ab438807c3a277f9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Checkout_02A.fbx using Guid(8f7e48f6945327b4ab438807c3a277f9) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '4ba012d0b49da6df0fbf555c2adba19a') in 0.0337915 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Bathroom_Wall_Mount_04A.fbx
  artifactKey: Guid(ee26f161bec352746a7611ff02a5e705) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Bathroom_Wall_Mount_04A.fbx using Guid(ee26f161bec352746a7611ff02a5e705) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '3b58af579952cb0083a038380f891274') in 0.0318856 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Bed_01A.fbx
  artifactKey: Guid(ff78ce352d7886742869cafca199492d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Bed_01A.fbx using Guid(ff78ce352d7886742869cafca199492d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'aadd87bf75febbeb91264fee56f53a0f') in 0.0338787 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Bathroom_Wall_Shelves_01C.fbx
  artifactKey: Guid(6605045b6e23267409808806201cc837) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Bathroom_Wall_Shelves_01C.fbx using Guid(6605045b6e23267409808806201cc837) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '6adf21a0479cb6ebf66be33ee4bf4073') in 0.0389734 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Ceiling_Fixture_03B.fbx
  artifactKey: Guid(53df1a82c3b42024db33c7d896df28c5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Ceiling_Fixture_03B.fbx using Guid(53df1a82c3b42024db33c7d896df28c5) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '6f470769cb816b72c1fdb9e2db5a655e') in 0.0453914 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 20

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Bathroom_Wall_Mount_01A.fbx
  artifactKey: Guid(9bfcdce9b27de60459c9346a94d36e14) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Bathroom_Wall_Mount_01A.fbx using Guid(9bfcdce9b27de60459c9346a94d36e14) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'bea243f45fd6043284e6a49388e4bc98') in 0.032941 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Bathroom_Tub_01B.fbx
  artifactKey: Guid(df3fbad2dc500ea4ca869a1972359035) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Bathroom_Tub_01B.fbx using Guid(df3fbad2dc500ea4ca869a1972359035) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '891db0135c445fa9ad51ef9ab082e02e') in 0.0297455 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Concrete_Column_01A.fbx
  artifactKey: Guid(f878e45dc0f1be442a954ab339fe7de7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Concrete_Column_01A.fbx using Guid(f878e45dc0f1be442a954ab339fe7de7) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '2159ca5034decd3af7cca35e27212405') in 0.028469 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Bathroom_Toilet_01B.fbx
  artifactKey: Guid(f661c90eb543e7e478b315331cc711d3) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Bathroom_Toilet_01B.fbx using Guid(f661c90eb543e7e478b315331cc711d3) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '934110ca32e3a44b49f4927dc9f2dd8a') in 0.0422638 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Bush_01A.fbx
  artifactKey: Guid(31fa3fc5a4f073546b15434884185234) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Bush_01A.fbx using Guid(31fa3fc5a4f073546b15434884185234) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b475c01a0808290ed1c2b0b4a057c30e') in 0.0339931 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 14

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Bowl_02D.fbx
  artifactKey: Guid(88d15ec969b1063429be2247947ba5db) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Bowl_02D.fbx using Guid(88d15ec969b1063429be2247947ba5db) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '725597381667ed19ed4414eba78a9cf3') in 0.0282942 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Bathroom_Wall_Mount_05B.fbx
  artifactKey: Guid(716edbb2b82a3da4c956d509b78017f0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Bathroom_Wall_Mount_05B.fbx using Guid(716edbb2b82a3da4c956d509b78017f0) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'aed4fd8d786e9a63f839434bac8e39b5') in 0.0298337 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Bathroom_Wall_Mount_03A.fbx
  artifactKey: Guid(b4bf85281bc0e0248ac6f46dd46caad8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Bathroom_Wall_Mount_03A.fbx using Guid(b4bf85281bc0e0248ac6f46dd46caad8) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '4fb6805cd617bb10f802e82b531c310f') in 0.0304409 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Carton_Juice_01B.fbx
  artifactKey: Guid(2dddaea884c84ec43ac2c9b9e1bfab39) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Carton_Juice_01B.fbx using Guid(2dddaea884c84ec43ac2c9b9e1bfab39) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '60baceefb768cf8feb8c1f6a99d4cb7c') in 0.0383684 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000060 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Can_01C.fbx
  artifactKey: Guid(2b9be1feb67d0c14a90b607e44356468) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Can_01C.fbx using Guid(2b9be1feb67d0c14a90b607e44356468) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '857756bd01971f67777a939a25f1ed6c') in 0.037216 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Bathroom_Tub_02B.fbx
  artifactKey: Guid(c2edd2b376ed22943bba3074ab95f6af) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Bathroom_Tub_02B.fbx using Guid(c2edd2b376ed22943bba3074ab95f6af) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c1597d61f647fc90a841cb205089f8fd') in 0.0364992 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Ceiling_Fixture_01A.fbx
  artifactKey: Guid(e14a9c14c3780304eace437fa8aec7c1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Ceiling_Fixture_01A.fbx using Guid(e14a9c14c3780304eace437fa8aec7c1) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'a969cdf621cf0647b2806e60bd9ff37e') in 0.0434897 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 18

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Bathroom_Sink_02A.fbx
  artifactKey: Guid(d221456ccdafd1d49a61e2f2f9cb88bb) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Bathroom_Sink_02A.fbx using Guid(d221456ccdafd1d49a61e2f2f9cb88bb) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '1524419aa2ccd382ea3aa0cfc79b8a6e') in 0.0358147 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Bathroom_Wall_Faucet_01A.fbx
  artifactKey: Guid(9953f4a302d840c4385d8d5f28f4c5c9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Bathroom_Wall_Faucet_01A.fbx using Guid(9953f4a302d840c4385d8d5f28f4c5c9) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '6e0e928201a022b4b8e98999be46f00a') in 0.0308927 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Books_01B.fbx
  artifactKey: Guid(532789238039f5a4eb6494ab7d6353fd) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Books_01B.fbx using Guid(532789238039f5a4eb6494ab7d6353fd) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd32844eb596b9c9917778009a67c7c5a') in 0.036952 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Bathroom_Wall_Mount_05C.fbx
  artifactKey: Guid(92cff0331099d964e877efa98d04a30a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Bathroom_Wall_Mount_05C.fbx using Guid(92cff0331099d964e877efa98d04a30a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '8b75268181b9338e0e7a8c005b8404e0') in 0.0356455 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000072 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Bed_02B.fbx
  artifactKey: Guid(9e4cc8589b251ed49ab4d88b010ec3ac) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Bed_02B.fbx using Guid(9e4cc8589b251ed49ab4d88b010ec3ac) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f93e59cbc522de0c68ee3bbd32a28cc1') in 0.0316114 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Bathroom_Sink_01A.fbx
  artifactKey: Guid(84a90e2063f46cf47877791873380fb4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Bathroom_Sink_01A.fbx using Guid(84a90e2063f46cf47877791873380fb4) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '8b5306f8ba3f672de444ba33935447db') in 0.030021 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0