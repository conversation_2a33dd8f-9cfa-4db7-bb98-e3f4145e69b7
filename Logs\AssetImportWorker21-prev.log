Using pre-set license
Built from '6000.0/staging' branch; Version is '6000.0.47f1 (2ad1ed33fd3b) revision 2806253'; Using compiler version '193933523'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit CoreSingleLanguage' Language: 'fr' Physical Memory: 16051 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker21
-projectPath
C:/Users/<USER>/ONU
-logFile
Logs/AssetImportWorker21.log
-srvPort
56204
-job-worker-count
7
-background-job-worker-count
8
-gc-helper-count
1
Successfully changed project path to: C:/Users/<USER>/ONU
C:/Users/<USER>/ONU
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [41484]  Target information:

Player connection [41484]  * "[IP] ************** [Port] 0 [Flags] 2 [Guid] 3475335885 [EditorId] 3475335885 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-34E5P9N) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [41484] Host joined multi-casting on [***********:54997]...
Player connection [41484] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 7
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 1.84 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.0.47f1 (2ad1ed33fd3b)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path C:/Users/<USER>/ONU/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce GTX 1650 (ID=0x1f9d)
    Vendor:   NVIDIA
    VRAM:     3937 MB
    Driver:   32.0.15.7602
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56740
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Registered in 0.003576 seconds.
- Loaded All Assemblies, in  0.422 seconds
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 276 ms
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.697 seconds
Domain Reload Profiling: 1119ms
	BeginReloadAssembly (133ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (39ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (55ms)
	LoadAllAssembliesAndSetupDomain (183ms)
		LoadAssemblies (131ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (180ms)
			TypeCache.Refresh (178ms)
				TypeCache.ScanAssembly (165ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (698ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (650ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (371ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (55ms)
			ProcessInitializeOnLoadAttributes (135ms)
			ProcessInitializeOnLoadMethodAttributes (85ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.854 seconds
Refreshing native plugins compatible for Editor in 2.37 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
Package Manager log level set to [2]
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  0.951 seconds
Domain Reload Profiling: 1803ms
	BeginReloadAssembly (200ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (8ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (35ms)
	RebuildCommonClasses (37ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (43ms)
	LoadAllAssembliesAndSetupDomain (560ms)
		LoadAssemblies (383ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (292ms)
			TypeCache.Refresh (213ms)
				TypeCache.ScanAssembly (194ms)
			BuildScriptInfoCaches (60ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (952ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (748ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (10ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (154ms)
			ProcessInitializeOnLoadAttributes (452ms)
			ProcessInitializeOnLoadMethodAttributes (120ms)
			AfterProcessingInitializeOnLoad (7ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.01 seconds
Refreshing native plugins compatible for Editor in 5.75 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 198 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6205 unused Assets / (7.3 MB). Loaded Objects now: 6851.
Memory consumption went from 170.7 MB to 163.5 MB.
Total: 17.019100 ms (FindLiveObjects: 2.681700 ms CreateObjectMapping: 1.041400 ms MarkObjects: 7.748500 ms  DeleteObjects: 5.544000 ms)

========================================================================
Received Import Request.
  Time since last request: 1483224.025114 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Bush_01B.fbx
  artifactKey: Guid(190b9e9c966a0304bb4a87c69c1dada6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Bush_01B.fbx using Guid(190b9e9c966a0304bb4a87c69c1dada6) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0e4974b1057982c201ec68b850be3a59') in 4.9319817 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 15

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Bookshelf_01A.fbx
  artifactKey: Guid(f63e29ffd4df7b1459859166cbc50312) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Bookshelf_01A.fbx using Guid(f63e29ffd4df7b1459859166cbc50312) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '78a014091ba4ac28c24ee4c184950dfa') in 0.0390473 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Bathroom_Wall_Mirror_01B.fbx
  artifactKey: Guid(de556311a704e7141a5bcd85fb040a76) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Bathroom_Wall_Mirror_01B.fbx using Guid(de556311a704e7141a5bcd85fb040a76) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0fef10d289275354afad9dc72c304825') in 0.040428 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Coffee_Machine_01A.fbx
  artifactKey: Guid(31207d62d227ec84599a33adf884b553) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Coffee_Machine_01A.fbx using Guid(31207d62d227ec84599a33adf884b553) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'bb84f093f0fc44f23c6f85040f367f66') in 0.037638 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Bathroom_Wall_Mirror_03A.fbx
  artifactKey: Guid(cf1c03cf47aed4f4e91253f57e914f61) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Bathroom_Wall_Mirror_03A.fbx using Guid(cf1c03cf47aed4f4e91253f57e914f61) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f13c54b72843e5f28276e5fd2668b049') in 0.0318838 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Coffee_Pot_01A.fbx
  artifactKey: Guid(feb8da4842801f44598ea5b97a60e2a1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Coffee_Pot_01A.fbx using Guid(feb8da4842801f44598ea5b97a60e2a1) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e86dc982441ebdbd47556c7c1ae0e578') in 0.0322638 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Bathroom_Wall_Mount_02A.fbx
  artifactKey: Guid(be2f32f8ed7c5f14098e538a92f4688b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Bathroom_Wall_Mount_02A.fbx using Guid(be2f32f8ed7c5f14098e538a92f4688b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '8add480f12ef83094bf08bd21322637e') in 0.0340101 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000056 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Bench_01A.fbx
  artifactKey: Guid(6942143718f5ed34a877d1ea265133ee) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Bench_01A.fbx using Guid(6942143718f5ed34a877d1ea265133ee) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f4b14f9826e432e6a82c347ebb346b5b') in 0.0338862 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Coffee_Mug_02A.fbx
  artifactKey: Guid(3c79c18dc62049a4ba6c387ab703177d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Coffee_Mug_02A.fbx using Guid(3c79c18dc62049a4ba6c387ab703177d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ed704f8f05debfb0c64e91c67541abff') in 0.0285096 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Characters Source/TSP_Male_Character_03.prefab
  artifactKey: Guid(a51ab05610911174ba2157d2f64c54f6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Characters Source/TSP_Male_Character_03.prefab using Guid(a51ab05610911174ba2157d2f64c54f6) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '21ed153119af2c4ec334608d96968704') in 0.0702348 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 178

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Bottle_01B.fbx
  artifactKey: Guid(fb04f26049242f947bad4f26f30a21e3) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Bottle_01B.fbx using Guid(fb04f26049242f947bad4f26f30a21e3) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e1bdac29600e74847633b556840c5886') in 0.0376236 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000063 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Ceiling_Fixture_04B.fbx
  artifactKey: Guid(192ef0be6f8c40a4cae3c2d85a251751) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Ceiling_Fixture_04B.fbx using Guid(192ef0be6f8c40a4cae3c2d85a251751) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ddfcba4cd74ebeb462fc353a17b1af49') in 0.0494318 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 18

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Bathroom_Wall_Shelves_01A.fbx
  artifactKey: Guid(edc9812813a958849b5a148e3589d6f3) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Bathroom_Wall_Shelves_01A.fbx using Guid(edc9812813a958849b5a148e3589d6f3) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '18ab7475b1b6d3da8ca11ba174d5c541') in 0.0385515 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Checkout_01A.fbx
  artifactKey: Guid(5777ab462fb0ffa419ad3ebfd7754090) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Checkout_01A.fbx using Guid(5777ab462fb0ffa419ad3ebfd7754090) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '4497b34518accd9872a2879fef5a85f8') in 0.0419491 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Bathroom_Wall_Faucet_02B.fbx
  artifactKey: Guid(5f72a7413953a0b42b7514a2ed62cef9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Bathroom_Wall_Faucet_02B.fbx using Guid(5f72a7413953a0b42b7514a2ed62cef9) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'cb2b8dd998b726b424f28a5233b7bb09') in 0.0365289 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Canopy_01C.fbx
  artifactKey: Guid(1a89f075e891c15459d16f90d02ec13d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Canopy_01C.fbx using Guid(1a89f075e891c15459d16f90d02ec13d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0d548ad5e477cae87e6d6b98df814a0e') in 0.0348979 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Bathroom_Wall_Mirror_02A.fbx
  artifactKey: Guid(5ccb92e1623637647912749ec593894d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Bathroom_Wall_Mirror_02A.fbx using Guid(5ccb92e1623637647912749ec593894d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '735bbf7ea0b7658011e84f7458410ee3') in 0.0285556 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Bathroom_Wall_Cabinet_01C.fbx
  artifactKey: Guid(a910fbbe34c9e2f4ba28945027ab5c82) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Bathroom_Wall_Cabinet_01C.fbx using Guid(a910fbbe34c9e2f4ba28945027ab5c82) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '2571475fdc5acb66bc1e3c8c14f098f5') in 0.041582 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0