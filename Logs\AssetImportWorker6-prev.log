Using pre-set license
Built from '6000.0/staging' branch; Version is '6000.0.47f1 (2ad1ed33fd3b) revision 2806253'; Using compiler version '193933523'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit CoreSingleLanguage' Language: 'fr' Physical Memory: 16051 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker6
-projectPath
C:/Users/<USER>/ONU
-logFile
Logs/AssetImportWorker6.log
-srvPort
56204
-job-worker-count
7
-background-job-worker-count
8
-gc-helper-count
1
Successfully changed project path to: C:/Users/<USER>/ONU
C:/Users/<USER>/ONU
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [38508]  Target information:

Player connection [38508]  * "[IP] ************** [Port] 0 [Flags] 2 [Guid] 511608882 [EditorId] 511608882 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-34E5P9N) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [38508] Host joined multi-casting on [***********:54997]...
Player connection [38508] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 7
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 16.13 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.0.47f1 (2ad1ed33fd3b)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path C:/Users/<USER>/ONU/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce GTX 1650 (ID=0x1f9d)
    Vendor:   NVIDIA
    VRAM:     3937 MB
    Driver:   32.0.15.7602
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56668
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Registered in 0.005762 seconds.
- Loaded All Assemblies, in  0.437 seconds
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 285 ms
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.566 seconds
Domain Reload Profiling: 1002ms
	BeginReloadAssembly (214ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (30ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (44ms)
	LoadAllAssembliesAndSetupDomain (137ms)
		LoadAssemblies (213ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (133ms)
			TypeCache.Refresh (132ms)
				TypeCache.ScanAssembly (121ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (0ms)
	FinalizeReload (566ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (530ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (356ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (38ms)
			ProcessInitializeOnLoadAttributes (86ms)
			ProcessInitializeOnLoadMethodAttributes (46ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.817 seconds
Refreshing native plugins compatible for Editor in 1.97 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
Package Manager log level set to [2]
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  1.049 seconds
Domain Reload Profiling: 1858ms
	BeginReloadAssembly (128ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (22ms)
	RebuildCommonClasses (27ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (31ms)
	LoadAllAssembliesAndSetupDomain (614ms)
		LoadAssemblies (485ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (205ms)
			TypeCache.Refresh (153ms)
				TypeCache.ScanAssembly (136ms)
			BuildScriptInfoCaches (41ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (1049ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (486ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (9ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (80ms)
			ProcessInitializeOnLoadAttributes (296ms)
			ProcessInitializeOnLoadMethodAttributes (94ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (5ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.03 seconds
Refreshing native plugins compatible for Editor in 3.28 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 195 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6205 unused Assets / (5.1 MB). Loaded Objects now: 6839.
Memory consumption went from 156.2 MB to 151.1 MB.
Total: 7.765100 ms (FindLiveObjects: 0.481900 ms CreateObjectMapping: 0.570800 ms MarkObjects: 4.263000 ms  DeleteObjects: 2.447000 ms)

========================================================================
Received Import Request.
  Time since last request: 1482851.087494 seconds.
  path: Assets/Toon Suburban Pack/Textures/TSP_Atlas_Lights_1A_D.psd
  artifactKey: Guid(34c7558395b7455438c7cb857300d111) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Textures/TSP_Atlas_Lights_1A_D.psd using Guid(34c7558395b7455438c7cb857300d111) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'da272b565bc8daaf61a9789280e9fa83') in 0.1122144 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000058 seconds.
  path: Assets/Toon Suburban Pack/Textures/TSP_Atlas_Vegetation_1A_D.psd
  artifactKey: Guid(c29d234c2af8adb4a9571de6e14965f8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Textures/TSP_Atlas_Vegetation_1A_D.psd using Guid(c29d234c2af8adb4a9571de6e14965f8) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '3638ecfe1b153e7a1dcd7594995cb3a2') in 0.0472623 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/Toon Suburban Pack/Textures/TSP_Toon_Ramp_1C_D.psd
  artifactKey: Guid(b9013c7ea902e7f4c8467965acbce1f2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Textures/TSP_Toon_Ramp_1C_D.psd using Guid(b9013c7ea902e7f4c8467965acbce1f2) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e73a2720d5ac0118d48e64ff592c73f0') in 0.0202724 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 7.674980 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Vehicles/TSP_Lawnmower_01A.prefab
  artifactKey: Guid(1d5e08f2d0bb14d4db1ef48c224339d2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Vehicles/TSP_Lawnmower_01A.prefab using Guid(1d5e08f2d0bb14d4db1ef48c224339d2) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '166d6e6b1f20d18cc18f859c5f73a5e0') in 0.4773319 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 42

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Vehicles/TSP_Wheelbarow_01A.prefab
  artifactKey: Guid(843f05b4c9f06914794ca5b04998a9f5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Vehicles/TSP_Wheelbarow_01A.prefab using Guid(843f05b4c9f06914794ca5b04998a9f5) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '7af9bb38bbb43bfd4e4420b3ad804309') in 0.0332011 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 18

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Vehicles/TSP_Motorcycle_01A.prefab
  artifactKey: Guid(ca211a9d75ff42643af60182647c0538) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Vehicles/TSP_Motorcycle_01A.prefab using Guid(ca211a9d75ff42643af60182647c0538) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b18993b2a6ac5e43ba8096856ed42a8d') in 0.0875251 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 53

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Vehicles/TSP_Sedan_01C.prefab
  artifactKey: Guid(23dddd222adc2ca4b8fbf9040f5ba039) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Vehicles/TSP_Sedan_01C.prefab using Guid(23dddd222adc2ca4b8fbf9040f5ba039) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '717958be7d8451694f37f544bea77124') in 0.0667154 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 97

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Vehicles/TSP_SUV_01B.prefab
  artifactKey: Guid(493be21f1d662b140a233c5aa1908242) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Vehicles/TSP_SUV_01B.prefab using Guid(493be21f1d662b140a233c5aa1908242) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '05433d9a49936c7ce6620f483ce5215b') in 0.0339938 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 84

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Vehicles/TSP_Sportscar_01A.prefab
  artifactKey: Guid(f536fbf5ebb3c9d44bc71c833c34f0e1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Vehicles/TSP_Sportscar_01A.prefab using Guid(f536fbf5ebb3c9d44bc71c833c34f0e1) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '71a3eef40f31a6703fa43b59c691f277') in 0.031142 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 94

========================================================================
Received Import Request.
  Time since last request: 0.000081 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Vehicles/TSP_SUV_01A.prefab
  artifactKey: Guid(69b9915500a4dd749a27789faac5cd24) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Vehicles/TSP_SUV_01A.prefab using Guid(69b9915500a4dd749a27789faac5cd24) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '767f89e8087964c1d1ee4b3eb85a0b96') in 0.0336668 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 84

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Vehicles/TSP_Minivan_01B.prefab
  artifactKey: Guid(21a984a9023cc4c4f8d18375300cb305) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Vehicles/TSP_Minivan_01B.prefab using Guid(21a984a9023cc4c4f8d18375300cb305) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f62316e687d24a27f32668a459e57550') in 0.0328926 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 89

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Vehicles/TSP_SUV_02C.prefab
  artifactKey: Guid(672fed292310607458eb14f7d903d879) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Vehicles/TSP_SUV_02C.prefab using Guid(672fed292310607458eb14f7d903d879) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e374eb21c5ad4e210a706c7c5afcd5c2') in 0.0334486 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 89

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Vehicles/TSP_Scooter_01B.prefab
  artifactKey: Guid(9f1b474b7bb9dd44ebf1edb875455c4d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Vehicles/TSP_Scooter_01B.prefab using Guid(9f1b474b7bb9dd44ebf1edb875455c4d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '276be5a017651b4beb9a3d28b6c6102f') in 0.0284009 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 43

========================================================================
Received Import Request.
  Time since last request: 0.000059 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Vehicles/TSP_Minivan_01C.prefab
  artifactKey: Guid(e67bf4dcbec34394197a786f121f1936) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Vehicles/TSP_Minivan_01C.prefab using Guid(e67bf4dcbec34394197a786f121f1936) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '9775fa24473e5e7999eb6df21303db55') in 0.0267802 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 89

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Vehicles/TSP_Scooter_01A.prefab
  artifactKey: Guid(bd33e554b7e7d644a82fdbe224f2fefa) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Vehicles/TSP_Scooter_01A.prefab using Guid(bd33e554b7e7d644a82fdbe224f2fefa) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'a382a35b0d547f96073653592cbbf22b') in 0.0314859 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 43

========================================================================
Received Import Request.
  Time since last request: 22.710335 seconds.
  path: Assets/Toon Suburban Pack/Models/Materials/Glass_Vehicles_01A_D.mat
  artifactKey: Guid(66004191299921f43ba2b97c1921ba6c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/Materials/Glass_Vehicles_01A_D.mat using Guid(66004191299921f43ba2b97c1921ba6c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '6df28d769e1abd9169eb7c346c5686a6') in 0.0666693 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/Toon Suburban Pack/Models/Materials/TSP_Atlas_Vegetation_1A_D.mat
  artifactKey: Guid(2517d9ac1c815e447915e010558d1e0a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/Materials/TSP_Atlas_Vegetation_1A_D.mat using Guid(2517d9ac1c815e447915e010558d1e0a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ed5ef8facdd9d34459b1e342bb009884') in 0.049758 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 4

========================================================================
Received Import Request.
  Time since last request: 24.819999 seconds.
  path: Assets/Settings/DefaultVolumeProfile.asset
  artifactKey: Guid(ab09877e2e707104187f6f83e2f62510) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Settings/DefaultVolumeProfile.asset using Guid(ab09877e2e707104187f6f83e2f62510) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'a2acb71d9dfeb98ee9fc85ff60fd1ae8') in 0.3164899 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000060 seconds.
  path: Assets/Settings/Mobile_RPAsset.asset
  artifactKey: Guid(5e6cbd92db86f4b18aec3ed561671858) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Settings/Mobile_RPAsset.asset using Guid(5e6cbd92db86f4b18aec3ed561671858) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '576aa248e43bc7b15718210dceabfa50') in 0.0174741 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000064 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Bathroom_Wall_Mirror_02B.fbx
  artifactKey: Guid(c4178c3ac8455c74d9af41205b8d2c1b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Toon Suburban Pack/Models/TSP_Bathroom_Wall_Mirror_02B.fbx using Guid(c4178c3ac8455c74d9af41205b8d2c1b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '52d1f4c994b94665f42d2e2fae8fac32') in 0.1772348 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Characters Variants/TSP_Male_Character_02C.prefab
  artifactKey: Guid(4ef6b1e8ec173e94191d9aa742d1f863) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Toon Suburban Pack/Prefabs/Characters Variants/TSP_Male_Character_02C.prefab using Guid(4ef6b1e8ec173e94191d9aa742d1f863) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'fabc6b2112cfa2b3e0db28985924395c') in 0.0593964 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 161

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Exterior Props/TSP_Pavement_01A_Round.prefab
  artifactKey: Guid(9d5f11a01abba1343bf2df24fab358f8) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Toon Suburban Pack/Prefabs/Exterior Props/TSP_Pavement_01A_Round.prefab using Guid(9d5f11a01abba1343bf2df24fab358f8) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'f26d5b911adbbc7777b409b736eeaf80') in 0.0331989 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Bathroom_Wall_Cabinet_01B.fbx
  artifactKey: Guid(d98475b88b8b00d4a959ce1253d3689e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Toon Suburban Pack/Models/TSP_Bathroom_Wall_Cabinet_01B.fbx using Guid(d98475b88b8b00d4a959ce1253d3689e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3a71375609fcb9c41c659ceaf4ee9ccc') in 0.0310743 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 14

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Toon Suburban Pack/Scenes/Sample Scene.unity
  artifactKey: Guid(609d223574134cb4893fe24a6220939b) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Toon Suburban Pack/Scenes/Sample Scene.unity using Guid(609d223574134cb4893fe24a6220939b) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter)Opening scene 'Assets/Toon Suburban Pack/Scenes/Sample Scene.unity additively'
Loaded scene 'Assets/Toon Suburban Pack/Scenes/Sample Scene.unity'
	Deserialize:            765.849 ms
	Integration:            4181.789 ms
	Integration of assets:  0.437 ms
	Thread Wait Time:       -0.419 ms
	Total Operation Time:   4947.655 ms
 -> (artifact id: '466519bd3fd88147ee7b2b7849e8d4b6') in 34.9671636 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 59854

Editor requested this worker to shutdown with reason: balancing worker count downwards
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0