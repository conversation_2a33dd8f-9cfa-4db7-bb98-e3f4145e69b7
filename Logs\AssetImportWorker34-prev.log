Using pre-set license
Built from '6000.0/staging' branch; Version is '6000.0.47f1 (2ad1ed33fd3b) revision 2806253'; Using compiler version '193933523'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit CoreSingleLanguage' Language: 'fr' Physical Memory: 16051 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker34
-projectPath
C:/Users/<USER>/ONU
-logFile
Logs/AssetImportWorker34.log
-srvPort
56204
-job-worker-count
7
-background-job-worker-count
8
-gc-helper-count
1
Successfully changed project path to: C:/Users/<USER>/ONU
C:/Users/<USER>/ONU
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [54380]  Target information:

Player connection [54380]  * "[IP] *********** [Port] 0 [Flags] 2 [Guid] 3988742231 [EditorId] 3988742231 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-34E5P9N) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [54380] Host joined multi-casting on [***********:54997]...
Player connection [54380] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 7
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 231.64 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.0.47f1 (2ad1ed33fd3b)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path C:/Users/<USER>/ONU/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce GTX 1650 (ID=0x1f9d)
    Vendor:   NVIDIA
    VRAM:     3937 MB
    Driver:   32.0.15.7602
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56028
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Registered in 0.004789 seconds.
- Loaded All Assemblies, in  2.166 seconds
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 382 ms
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.691 seconds
Domain Reload Profiling: 2856ms
	BeginReloadAssembly (133ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (424ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (42ms)
	LoadAllAssembliesAndSetupDomain (1558ms)
		LoadAssemblies (132ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (1555ms)
			TypeCache.Refresh (1553ms)
				TypeCache.ScanAssembly (1542ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (0ms)
	FinalizeReload (692ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (653ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (462ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (39ms)
			ProcessInitializeOnLoadAttributes (98ms)
			ProcessInitializeOnLoadMethodAttributes (50ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.261 seconds
Refreshing native plugins compatible for Editor in 1.49 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation. ---> System.NullReferenceException: Object reference not set to an instance of an object
  at Unity.Multiplayer.Playmode.Workflow.Editor.ProjectDataStore..ctor (System.String directory) [0x0006e] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Configuration\ProjectDataStore.cs:55 
  at Unity.Multiplayer.Playmode.Workflow.Editor.ProjectDataStore.GetMain () [0x00000] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Configuration\ProjectDataStore.cs:36 
  at Unity.Multiplayer.Playmode.Workflow.Editor.WorkflowMainEditorContext..ctor (Unity.Multiplayer.Playmode.VirtualProjects.Editor.MainEditorContext mainEditorContext) [0x0002c] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Contexts\WorkflowMainEditorContext.cs:15 
  at Unity.Multiplayer.Playmode.Workflow.Editor.VirtualProjectWorkflow.InitializeMPPMContexts () [0x00018] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Workflow\VirtualProjectWorkflow.cs:198 
  at Unity.Multiplayer.Playmode.Workflow.Editor.VirtualProjectWorkflow+<>c.<.cctor>b__22_0 () [0x00036] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Workflow\VirtualProjectWorkflow.cs:85 
  at Unity.Multiplayer.Playmode.VirtualProjects.Editor.EditorContexts.SendReadyEvent () [0x00038] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\VirtualProjects\Editor\Contexts\EditorContexts.cs:56 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <********************************>:0 
   --- End of inner exception stack trace ---
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x00083] in <********************************>:0 
  at System.Reflection.MethodBase.Invoke (System.Object obj, System.Object[] parameters) [0x00000] in <********************************>:0 
  at UnityEditor.EditorAssemblies.ProcessInitializeOnLoadMethodAttributes () [0x00056] in <f8e1f14ef858402482f58cef2e987b5b>:0 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

Mirror | mirror-networking.com | discord.gg/N9QVxbM
Mono: successfully reloaded assembly
Package Manager log level set to [2]
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  1.746 seconds
Domain Reload Profiling: 3005ms
	BeginReloadAssembly (130ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (22ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (30ms)
	LoadAllAssembliesAndSetupDomain (1062ms)
		LoadAssemblies (350ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (790ms)
			TypeCache.Refresh (725ms)
				TypeCache.ScanAssembly (544ms)
			BuildScriptInfoCaches (46ms)
			ResolveRequiredComponents (15ms)
	FinalizeReload (1747ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (699ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (9ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (96ms)
			ProcessInitializeOnLoadAttributes (384ms)
			ProcessInitializeOnLoadMethodAttributes (200ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (5ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.02 seconds
Refreshing native plugins compatible for Editor in 2.87 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 219 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7348 unused Assets / (7.6 MB). Loaded Objects now: 8010.
Memory consumption went from 188.7 MB to 181.1 MB.
Total: 16.199500 ms (FindLiveObjects: 0.854400 ms CreateObjectMapping: 1.874500 ms MarkObjects: 8.594000 ms  DeleteObjects: 4.873000 ms)

========================================================================
Received Import Request.
  Time since last request: 1567137.175798 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Lawnmower_01A.fbx
  artifactKey: Guid(5bb601e452e03944485537b7702b6161) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Lawnmower_01A.fbx using Guid(5bb601e452e03944485537b7702b6161) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '106e9b72a0b303dc3606810a40d9e839') in 0.5600664 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 39

========================================================================
Received Import Request.
  Time since last request: 3.930176 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Microwave_01A.fbx
  artifactKey: Guid(2e2d9a915dd390e4194d56ce6e2b7db2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Microwave_01A.fbx using Guid(2e2d9a915dd390e4194d56ce6e2b7db2) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '44e5f7cf4d8232461137dee3ef38c479') in 0.0564168 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 18

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Newspaper_01A.fbx
  artifactKey: Guid(c419d577240992c41a772668fd97a702) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Newspaper_01A.fbx using Guid(c419d577240992c41a772668fd97a702) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '4b05b3b94d6fe27fff16ccd992f7709e') in 0.0300766 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Ottoman_01A.fbx
  artifactKey: Guid(814ac3bda72cecb48acb029da247f3a5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Ottoman_01A.fbx using Guid(814ac3bda72cecb48acb029da247f3a5) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '4a1edbc13740232a997f0b49944ea918') in 0.0263914 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Nightstand_01C.fbx
  artifactKey: Guid(fddbfb49fa773894ebb77477bdf0f9d6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Nightstand_01C.fbx using Guid(fddbfb49fa773894ebb77477bdf0f9d6) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '716992b56377af94d4b2b88ce4fdb9d4') in 0.0314228 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Nightstand_01B.fbx
  artifactKey: Guid(bce2c0630fc73cf4aa3fc07297db2207) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Nightstand_01B.fbx using Guid(bce2c0630fc73cf4aa3fc07297db2207) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '9a64c41afb606081795aeff31c099552') in 0.0328751 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Nightstand_01A.fbx
  artifactKey: Guid(b6ee6632a76aac54f993f8247b47ada6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Nightstand_01A.fbx using Guid(b6ee6632a76aac54f993f8247b47ada6) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'cda8eb7d7d08d4154686cf11f5cac537') in 0.0283074 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0