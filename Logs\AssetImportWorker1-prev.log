Using pre-set license
Built from '6000.0/staging' branch; Version is '6000.0.47f1 (2ad1ed33fd3b) revision 2806253'; Using compiler version '193933523'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit CoreSingleLanguage' Language: 'fr' Physical Memory: 16051 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker1
-projectPath
C:/Users/<USER>/ONU
-logFile
Logs/AssetImportWorker1.log
-srvPort
56204
-job-worker-count
7
-background-job-worker-count
8
-gc-helper-count
1
Successfully changed project path to: C:/Users/<USER>/ONU
C:/Users/<USER>/ONU
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [37692]  Target information:

Player connection [37692]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 2741079089 [EditorId] 2741079089 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-34E5P9N) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [37692] Host joined multi-casting on [***********:54997]...
Player connection [37692] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 7
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 7.26 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.0.47f1 (2ad1ed33fd3b)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path C:/Users/<USER>/ONU/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce GTX 1650 (ID=0x1f9d)
    Vendor:   NVIDIA
    VRAM:     3937 MB
    Driver:   32.0.15.7602
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56532
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Registered in 0.003590 seconds.
- Loaded All Assemblies, in  0.697 seconds
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 1379 ms
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.094 seconds
Domain Reload Profiling: 2790ms
	BeginReloadAssembly (206ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (42ms)
	RebuildNativeTypeToScriptingClass (14ms)
	initialDomainReloadingComplete (58ms)
	LoadAllAssembliesAndSetupDomain (375ms)
		LoadAssemblies (204ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (371ms)
			TypeCache.Refresh (368ms)
				TypeCache.ScanAssembly (344ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (2095ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1997ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (1548ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (68ms)
			ProcessInitializeOnLoadAttributes (246ms)
			ProcessInitializeOnLoadMethodAttributes (131ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.059 seconds
Refreshing native plugins compatible for Editor in 10.78 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
Package Manager log level set to [2]
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  3.344 seconds
Domain Reload Profiling: 4400ms
	BeginReloadAssembly (285ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (43ms)
	RebuildCommonClasses (38ms)
	RebuildNativeTypeToScriptingClass (13ms)
	initialDomainReloadingComplete (44ms)
	LoadAllAssembliesAndSetupDomain (675ms)
		LoadAssemblies (529ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (316ms)
			TypeCache.Refresh (192ms)
				TypeCache.ScanAssembly (171ms)
			BuildScriptInfoCaches (74ms)
			ResolveRequiredComponents (42ms)
	FinalizeReload (3345ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (2627ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (51ms)
			SetLoadedEditorAssemblies (18ms)
			BeforeProcessingInitializeOnLoad (403ms)
			ProcessInitializeOnLoadAttributes (1679ms)
			ProcessInitializeOnLoadMethodAttributes (457ms)
			AfterProcessingInitializeOnLoad (20ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (26ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.03 seconds
Refreshing native plugins compatible for Editor in 12.01 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 191 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6182 unused Assets / (7.1 MB). Loaded Objects now: 6812.
Memory consumption went from 159.6 MB to 152.5 MB.
Total: 29.693800 ms (FindLiveObjects: 2.412100 ms CreateObjectMapping: 1.884600 ms MarkObjects: 15.680400 ms  DeleteObjects: 9.708800 ms)

========================================================================
Received Import Request.
  Time since last request: 1471224.813799 seconds.
  path: Assets/Readme.asset
  artifactKey: Guid(8105016687592461f977c054a80ce2f2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Readme.asset using Guid(8105016687592461f977c054a80ce2f2) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '2badcec6b3e6cb8db6a9827590e0b69f') in 0.0785077 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  3.082 seconds
Refreshing native plugins compatible for Editor in 11.79 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  2.999 seconds
Domain Reload Profiling: 6072ms
	BeginReloadAssembly (679ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (27ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (150ms)
	RebuildCommonClasses (125ms)
	RebuildNativeTypeToScriptingClass (54ms)
	initialDomainReloadingComplete (107ms)
	LoadAllAssembliesAndSetupDomain (2107ms)
		LoadAssemblies (1223ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (1201ms)
			TypeCache.Refresh (764ms)
				TypeCache.ScanAssembly (710ms)
			BuildScriptInfoCaches (380ms)
			ResolveRequiredComponents (44ms)
	FinalizeReload (3000ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (2116ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (51ms)
			SetLoadedEditorAssemblies (19ms)
			BeforeProcessingInitializeOnLoad (435ms)
			ProcessInitializeOnLoadAttributes (1356ms)
			ProcessInitializeOnLoadMethodAttributes (235ms)
			AfterProcessingInitializeOnLoad (20ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (58ms)
Refreshing native plugins compatible for Editor in 12.71 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 43 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6189 unused Assets / (7.3 MB). Loaded Objects now: 6841.
Memory consumption went from 149.5 MB to 142.2 MB.
Total: 28.613900 ms (FindLiveObjects: 1.354800 ms CreateObjectMapping: 1.555600 ms MarkObjects: 14.471600 ms  DeleteObjects: 11.228200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 10039.722706 seconds.
  path: Assets/Textures/Bark/Moss_Normal.png
  artifactKey: Guid(804264669471f584f8c86334239929b7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Textures/Bark/Moss_Normal.png using Guid(804264669471f584f8c86334239929b7) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '51f564930f4d4ceb3d7ac803fcbb5f55') in 0.6232886 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 152.493273 seconds.
  path: Assets/Trees/Source/Materials/URP/Falling_Leaves/Bushy_Leaf.mat
  artifactKey: Guid(4d88cd9cfbfe63b49b0371a046210a5e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Trees/Source/Materials/URP/Falling_Leaves/Bushy_Leaf.mat using Guid(4d88cd9cfbfe63b49b0371a046210a5e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f49258747dc7725de8f8630cfd8f7798') in 2.1908277 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 45.341216 seconds.
  path: Assets/Trees/Source/Textures/Leaves/Bambo_Leaves.png
  artifactKey: Guid(744eb5c98fa46704d9888bd432b4b808) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Trees/Source/Textures/Leaves/Bambo_Leaves.png using Guid(744eb5c98fa46704d9888bd432b4b808) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '5238930a59f129b34e3405ca1cbb9ba4') in 0.0281681 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/Trees/Source/Textures/Leaves/Bambo_Leaves_Normal.png
  artifactKey: Guid(96eafb686734f9a438fc417eb9af9027) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Trees/Source/Textures/Leaves/Bambo_Leaves_Normal.png using Guid(96eafb686734f9a438fc417eb9af9027) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '1e2935a2c329fd5b68a49fede11105b2') in 0.0334048 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/Trees/Source/Textures/Leaves/Blossom_Leaf.png
  artifactKey: Guid(020eb68e1b998954c8fbd6bf2f404ec4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Trees/Source/Textures/Leaves/Blossom_Leaf.png using Guid(020eb68e1b998954c8fbd6bf2f404ec4) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '84466dea08df46b95fbe0584583a8520') in 0.0268263 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000195 seconds.
  path: Assets/Trees/Source/Textures/Leaves/Blossom_Branch.png
  artifactKey: Guid(b9ba8a8ceb79275418fb46fb8b272d6a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Trees/Source/Textures/Leaves/Blossom_Branch.png using Guid(b9ba8a8ceb79275418fb46fb8b272d6a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '7ace8e418d5bf5e84e59827829e4fda0') in 0.0272846 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0