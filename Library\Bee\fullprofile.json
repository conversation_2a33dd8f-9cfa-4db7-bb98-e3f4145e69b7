{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 13804, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 13804, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 13804, "tid": 164368, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 13804, "tid": 164368, "ts": 1755547994951565, "dur": 1006, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 13804, "tid": 164368, "ts": 1755547994956195, "dur": 775, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 13804, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 13804, "tid": 1, "ts": 1755547993629608, "dur": 4558, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 13804, "tid": 1, "ts": 1755547993634169, "dur": 50108, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 13804, "tid": 1, "ts": 1755547993684288, "dur": 88507, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 13804, "tid": 164368, "ts": 1755547994956974, "dur": 9, "ph": "X", "name": "", "args": {}}, {"pid": 13804, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993628037, "dur": 7679, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993635718, "dur": 1307658, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993636787, "dur": 2375, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993639169, "dur": 1130, "ph": "X", "name": "ProcessMessages 17216", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993640301, "dur": 310, "ph": "X", "name": "ReadAsync 17216", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993640615, "dur": 12, "ph": "X", "name": "ProcessMessages 20542", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993640629, "dur": 99, "ph": "X", "name": "ReadAsync 20542", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993640733, "dur": 3, "ph": "X", "name": "ProcessMessages 2495", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993640738, "dur": 62, "ph": "X", "name": "ReadAsync 2495", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993640804, "dur": 2, "ph": "X", "name": "ProcessMessages 1635", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993640807, "dur": 100, "ph": "X", "name": "ReadAsync 1635", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993640912, "dur": 87, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993641000, "dur": 2, "ph": "X", "name": "ProcessMessages 1771", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993641004, "dur": 82, "ph": "X", "name": "ReadAsync 1771", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993641088, "dur": 84, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993641173, "dur": 1, "ph": "X", "name": "ProcessMessages 1573", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993641175, "dur": 89, "ph": "X", "name": "ReadAsync 1573", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993641265, "dur": 1, "ph": "X", "name": "ProcessMessages 1635", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993641267, "dur": 26, "ph": "X", "name": "ReadAsync 1635", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993641294, "dur": 1, "ph": "X", "name": "ProcessMessages 1246", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993641295, "dur": 61, "ph": "X", "name": "ReadAsync 1246", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993641358, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993641374, "dur": 14, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993641389, "dur": 13, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993641405, "dur": 13, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993641420, "dur": 13, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993641435, "dur": 22, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993641458, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993641475, "dur": 14, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993641491, "dur": 18, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993641510, "dur": 21, "ph": "X", "name": "ReadAsync 223", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993641533, "dur": 13, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993641548, "dur": 14, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993641564, "dur": 28, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993641595, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993641610, "dur": 15, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993641627, "dur": 17, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993641647, "dur": 14, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993641663, "dur": 16, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993641682, "dur": 13, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993641696, "dur": 13, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993641711, "dur": 14, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993641727, "dur": 17, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993641746, "dur": 17, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993641764, "dur": 17, "ph": "X", "name": "ReadAsync 522", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993641783, "dur": 19, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993641804, "dur": 15, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993641821, "dur": 13, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993641836, "dur": 13, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993641851, "dur": 14, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993641867, "dur": 24, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993641893, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993641909, "dur": 21, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993641932, "dur": 15, "ph": "X", "name": "ReadAsync 563", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993641949, "dur": 17, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993641968, "dur": 13, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993641982, "dur": 57, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993642042, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993642044, "dur": 54, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993642099, "dur": 1, "ph": "X", "name": "ProcessMessages 1913", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993642101, "dur": 15, "ph": "X", "name": "ReadAsync 1913", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993642119, "dur": 15, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993642135, "dur": 16, "ph": "X", "name": "ReadAsync 482", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993642153, "dur": 18, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993642174, "dur": 38, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993642213, "dur": 13, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993642228, "dur": 38, "ph": "X", "name": "ReadAsync 88", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993642268, "dur": 14, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993642284, "dur": 14, "ph": "X", "name": "ReadAsync 547", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993642300, "dur": 25, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993642327, "dur": 17, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993642346, "dur": 13, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993642360, "dur": 13, "ph": "X", "name": "ReadAsync 98", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993642375, "dur": 13, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993642390, "dur": 14, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993642406, "dur": 14, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993642422, "dur": 19, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993642444, "dur": 14, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993642460, "dur": 14, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993642476, "dur": 16, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993642494, "dur": 14, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993642511, "dur": 20, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993642533, "dur": 14, "ph": "X", "name": "ReadAsync 609", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993642549, "dur": 13, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993642564, "dur": 13, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993642579, "dur": 17, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993642598, "dur": 21, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993642621, "dur": 15, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993642639, "dur": 16, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993642657, "dur": 13, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993642672, "dur": 14, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993642688, "dur": 18, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993642708, "dur": 11, "ph": "X", "name": "ReadAsync 127", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993642721, "dur": 33, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993642757, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993642772, "dur": 13, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993642788, "dur": 14, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993642804, "dur": 14, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993642820, "dur": 12, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993642834, "dur": 13, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993642849, "dur": 14, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993642864, "dur": 15, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993642881, "dur": 16, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993642900, "dur": 14, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993642916, "dur": 14, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993642932, "dur": 13, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993642947, "dur": 14, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993642962, "dur": 13, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993642977, "dur": 14, "ph": "X", "name": "ReadAsync 137", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993642992, "dur": 13, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993643008, "dur": 14, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993643024, "dur": 14, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993643040, "dur": 13, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993643055, "dur": 302, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993643358, "dur": 2, "ph": "X", "name": "ProcessMessages 4325", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993643361, "dur": 21, "ph": "X", "name": "ReadAsync 4325", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993643385, "dur": 91, "ph": "X", "name": "ReadAsync 743", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993643477, "dur": 21, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993643501, "dur": 16, "ph": "X", "name": "ReadAsync 1055", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993643518, "dur": 15, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993643535, "dur": 17, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993643554, "dur": 16, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993643572, "dur": 13, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993643587, "dur": 17, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993643606, "dur": 15, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993643623, "dur": 16, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993643641, "dur": 16, "ph": "X", "name": "ReadAsync 522", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993643660, "dur": 17, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993643678, "dur": 14, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993643694, "dur": 146, "ph": "X", "name": "ReadAsync 79", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993643844, "dur": 120, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993643968, "dur": 1, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993643972, "dur": 60, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993644033, "dur": 2, "ph": "X", "name": "ProcessMessages 3194", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993644036, "dur": 14, "ph": "X", "name": "ReadAsync 3194", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993644052, "dur": 22, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993644077, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993644097, "dur": 24, "ph": "X", "name": "ReadAsync 491", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993644123, "dur": 14, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993644138, "dur": 12, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993644152, "dur": 14, "ph": "X", "name": "ReadAsync 167", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993644168, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993644196, "dur": 15, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993644212, "dur": 19, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993644233, "dur": 13, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993644249, "dur": 12, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993644263, "dur": 31, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993644295, "dur": 14, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993644311, "dur": 22, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993644335, "dur": 32, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993644369, "dur": 17, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993644389, "dur": 12, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993644403, "dur": 27, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993644431, "dur": 14, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993644448, "dur": 16, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993644465, "dur": 14, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993644481, "dur": 14, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993644497, "dur": 13, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993644511, "dur": 13, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993644526, "dur": 15, "ph": "X", "name": "ReadAsync 152", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993644543, "dur": 15, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993644560, "dur": 13, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993644575, "dur": 22, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993644599, "dur": 14, "ph": "X", "name": "ReadAsync 633", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993644614, "dur": 14, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993644630, "dur": 15, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993644647, "dur": 14, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993644663, "dur": 17, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993644682, "dur": 14, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993644698, "dur": 13, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993644713, "dur": 14, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993644729, "dur": 13, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993644743, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993644759, "dur": 14, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993644775, "dur": 14, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993644791, "dur": 14, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993644807, "dur": 14, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993644823, "dur": 13, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993644838, "dur": 13, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993644852, "dur": 15, "ph": "X", "name": "ReadAsync 142", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993644869, "dur": 14, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993644885, "dur": 15, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993644902, "dur": 14, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993644918, "dur": 16, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993644936, "dur": 12, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993644949, "dur": 15, "ph": "X", "name": "ReadAsync 85", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993644966, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993644982, "dur": 13, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993644996, "dur": 17, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993645016, "dur": 16, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993645034, "dur": 14, "ph": "X", "name": "ReadAsync 111", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993645049, "dur": 15, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993645066, "dur": 12, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993645080, "dur": 13, "ph": "X", "name": "ReadAsync 73", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993645094, "dur": 14, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993645110, "dur": 14, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993645125, "dur": 17, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993645145, "dur": 15, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993645161, "dur": 13, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993645176, "dur": 12, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993645190, "dur": 31, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993645222, "dur": 14, "ph": "X", "name": "ReadAsync 187", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993645238, "dur": 17, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993645257, "dur": 17, "ph": "X", "name": "ReadAsync 587", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993645275, "dur": 25, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993645303, "dur": 15, "ph": "X", "name": "ReadAsync 645", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993645319, "dur": 14, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993645335, "dur": 14, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993645350, "dur": 20, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993645372, "dur": 73, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993645449, "dur": 1, "ph": "X", "name": "ProcessMessages 326", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993645452, "dur": 41, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993645494, "dur": 1, "ph": "X", "name": "ProcessMessages 1925", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993645496, "dur": 16, "ph": "X", "name": "ReadAsync 1925", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993645513, "dur": 15, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993645530, "dur": 13, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993645545, "dur": 19, "ph": "X", "name": "ReadAsync 97", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993645566, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993645591, "dur": 16, "ph": "X", "name": "ReadAsync 724", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993645609, "dur": 13, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993645624, "dur": 14, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993645640, "dur": 11, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993645653, "dur": 13, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993645668, "dur": 121, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993645792, "dur": 39, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993645831, "dur": 2, "ph": "X", "name": "ProcessMessages 3859", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993645834, "dur": 14, "ph": "X", "name": "ReadAsync 3859", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993645850, "dur": 14, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993645865, "dur": 15, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993645882, "dur": 20, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993645904, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993645923, "dur": 14, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993645939, "dur": 14, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993645955, "dur": 14, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993645971, "dur": 15, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993645988, "dur": 13, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993646003, "dur": 15, "ph": "X", "name": "ReadAsync 171", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993646019, "dur": 16, "ph": "X", "name": "ReadAsync 146", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993646036, "dur": 13, "ph": "X", "name": "ReadAsync 214", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993646064, "dur": 21, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993646087, "dur": 20, "ph": "X", "name": "ReadAsync 1142", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993646109, "dur": 14, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993646125, "dur": 17, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993646144, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993646160, "dur": 15, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993646176, "dur": 16, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993646195, "dur": 13, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993646210, "dur": 21, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993646232, "dur": 12, "ph": "X", "name": "ReadAsync 571", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993646246, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993646262, "dur": 15, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993646280, "dur": 12, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993646294, "dur": 13, "ph": "X", "name": "ReadAsync 219", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993646309, "dur": 13, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993646324, "dur": 26, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993646352, "dur": 34, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993646388, "dur": 13, "ph": "X", "name": "ReadAsync 273", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993646403, "dur": 19, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993646424, "dur": 13, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993646439, "dur": 13, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993646454, "dur": 13, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993646469, "dur": 12, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993646483, "dur": 13, "ph": "X", "name": "ReadAsync 131", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993646498, "dur": 13, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993646513, "dur": 12, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993646527, "dur": 103, "ph": "X", "name": "ReadAsync 94", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993646632, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993646681, "dur": 1, "ph": "X", "name": "ProcessMessages 2669", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993646683, "dur": 17, "ph": "X", "name": "ReadAsync 2669", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993646703, "dur": 13, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993646718, "dur": 13, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993646732, "dur": 15, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993646750, "dur": 13, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993646764, "dur": 21, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993646787, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993646803, "dur": 14, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993646819, "dur": 12, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993646833, "dur": 14, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993646849, "dur": 13, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993646864, "dur": 22, "ph": "X", "name": "ReadAsync 211", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993646887, "dur": 16, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993646905, "dur": 25, "ph": "X", "name": "ReadAsync 616", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993646932, "dur": 1, "ph": "X", "name": "ProcessMessages 466", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993646934, "dur": 27, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993646962, "dur": 1, "ph": "X", "name": "ProcessMessages 849", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993646964, "dur": 14, "ph": "X", "name": "ReadAsync 849", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993646980, "dur": 13, "ph": "X", "name": "ReadAsync 205", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993646995, "dur": 13, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993647010, "dur": 14, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993647026, "dur": 15, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993647043, "dur": 14, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993647060, "dur": 13, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993647075, "dur": 13, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993647089, "dur": 13, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993647104, "dur": 17, "ph": "X", "name": "ReadAsync 129", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993647123, "dur": 14, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993647139, "dur": 14, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993647154, "dur": 19, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993647176, "dur": 13, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993647191, "dur": 12, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993647205, "dur": 13, "ph": "X", "name": "ReadAsync 273", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993647219, "dur": 16, "ph": "X", "name": "ReadAsync 59", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993647238, "dur": 13, "ph": "X", "name": "ReadAsync 551", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993647253, "dur": 17, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993647272, "dur": 137, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993647412, "dur": 1, "ph": "X", "name": "ProcessMessages 290", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993647414, "dur": 42, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993647457, "dur": 2, "ph": "X", "name": "ProcessMessages 2413", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993647460, "dur": 16, "ph": "X", "name": "ReadAsync 2413", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993647478, "dur": 20, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993647501, "dur": 13, "ph": "X", "name": "ReadAsync 734", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993647516, "dur": 15, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993647533, "dur": 15, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993647551, "dur": 12, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993647566, "dur": 12, "ph": "X", "name": "ReadAsync 85", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993647580, "dur": 14, "ph": "X", "name": "ReadAsync 135", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993647597, "dur": 15, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993647614, "dur": 20, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993647636, "dur": 15, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993647654, "dur": 14, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993647670, "dur": 17, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993647689, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993647709, "dur": 14, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993647725, "dur": 15, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993647742, "dur": 20, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993647764, "dur": 13, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993647779, "dur": 13, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993647794, "dur": 15, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993647810, "dur": 13, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993647825, "dur": 22, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993647849, "dur": 13, "ph": "X", "name": "ReadAsync 629", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993647864, "dur": 13, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993647879, "dur": 17, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993647898, "dur": 15, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993647915, "dur": 13, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993647930, "dur": 13, "ph": "X", "name": "ReadAsync 513", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993647945, "dur": 16, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993647963, "dur": 14, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993647980, "dur": 14, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993647995, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993648011, "dur": 20, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993648033, "dur": 19, "ph": "X", "name": "ReadAsync 543", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993648054, "dur": 14, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993648070, "dur": 13, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993648085, "dur": 13, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993648100, "dur": 17, "ph": "X", "name": "ReadAsync 127", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993648118, "dur": 15, "ph": "X", "name": "ReadAsync 480", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993648136, "dur": 18, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993648156, "dur": 40, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993648197, "dur": 1, "ph": "X", "name": "ProcessMessages 297", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993648199, "dur": 15, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993648217, "dur": 35, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993648254, "dur": 18, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993648274, "dur": 15, "ph": "X", "name": "ReadAsync 628", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993648291, "dur": 15, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993648308, "dur": 12, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993648322, "dur": 14, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993648339, "dur": 14, "ph": "X", "name": "ReadAsync 187", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993648355, "dur": 17, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993648374, "dur": 19, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993648395, "dur": 13, "ph": "X", "name": "ReadAsync 557", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993648410, "dur": 14, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993648426, "dur": 16, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993648445, "dur": 14, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993648463, "dur": 16, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993648481, "dur": 17, "ph": "X", "name": "ReadAsync 683", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993648501, "dur": 15, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993648518, "dur": 16, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993648536, "dur": 13, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993648552, "dur": 14, "ph": "X", "name": "ReadAsync 95", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993648568, "dur": 18, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993648588, "dur": 21, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993648612, "dur": 20, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993648634, "dur": 15, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993648652, "dur": 14, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993648668, "dur": 16, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993648686, "dur": 17, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993648704, "dur": 1, "ph": "X", "name": "ProcessMessages 537", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993648706, "dur": 19, "ph": "X", "name": "ReadAsync 537", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993648727, "dur": 16, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993648746, "dur": 14, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993648763, "dur": 19, "ph": "X", "name": "ReadAsync 175", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993648785, "dur": 17, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993648804, "dur": 19, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993648826, "dur": 122, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993648952, "dur": 1, "ph": "X", "name": "ProcessMessages 400", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993648954, "dur": 52, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993649009, "dur": 2, "ph": "X", "name": "ProcessMessages 3738", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993649013, "dur": 21, "ph": "X", "name": "ReadAsync 3738", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993649036, "dur": 28, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993649067, "dur": 15, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993649084, "dur": 12, "ph": "X", "name": "ReadAsync 557", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993649099, "dur": 13, "ph": "X", "name": "ReadAsync 93", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993649115, "dur": 36, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993649154, "dur": 14, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993649170, "dur": 17, "ph": "X", "name": "ReadAsync 546", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993649190, "dur": 14, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993649206, "dur": 14, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993649222, "dur": 19, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993649243, "dur": 1, "ph": "X", "name": "ProcessMessages 330", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993649244, "dur": 15, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993649262, "dur": 17, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993649281, "dur": 18, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993649302, "dur": 15, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993649319, "dur": 13, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993649335, "dur": 17, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993649355, "dur": 16, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993649374, "dur": 17, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993649393, "dur": 15, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993649409, "dur": 1, "ph": "X", "name": "ProcessMessages 385", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993649410, "dur": 14, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993649427, "dur": 16, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993649446, "dur": 19, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993649465, "dur": 4, "ph": "X", "name": "ProcessMessages 594", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993649470, "dur": 15, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993649488, "dur": 14, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993649505, "dur": 14, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993649522, "dur": 14, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993649540, "dur": 16, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993649558, "dur": 14, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993649575, "dur": 18, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993649596, "dur": 14, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993649613, "dur": 14, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993649629, "dur": 18, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993649649, "dur": 21, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993649673, "dur": 17, "ph": "X", "name": "ReadAsync 803", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993649692, "dur": 15, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993649709, "dur": 14, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993649726, "dur": 16, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993649745, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993649765, "dur": 16, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993649783, "dur": 15, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993649800, "dur": 15, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993649817, "dur": 14, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993649834, "dur": 13, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993649848, "dur": 14, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993649865, "dur": 15, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993649883, "dur": 26, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993649910, "dur": 29, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993649943, "dur": 14, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993649959, "dur": 15, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993649977, "dur": 12, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993649991, "dur": 20, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993650014, "dur": 1, "ph": "X", "name": "ProcessMessages 276", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993650016, "dur": 19, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993650039, "dur": 20, "ph": "X", "name": "ReadAsync 137", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993650062, "dur": 15, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993650079, "dur": 15, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993650096, "dur": 16, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993650115, "dur": 17, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993650135, "dur": 15, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993650153, "dur": 15, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993650170, "dur": 17, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993650189, "dur": 14, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993650206, "dur": 15, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993650224, "dur": 17, "ph": "X", "name": "ReadAsync 118", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993650243, "dur": 16, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993650263, "dur": 15, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993650281, "dur": 14, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993650297, "dur": 18, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993650319, "dur": 13, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993650334, "dur": 13, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993650349, "dur": 14, "ph": "X", "name": "ReadAsync 95", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993650367, "dur": 29, "ph": "X", "name": "ReadAsync 145", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993650398, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993650416, "dur": 16, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993650435, "dur": 14, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993650452, "dur": 298, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993650754, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993650757, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993650819, "dur": 3, "ph": "X", "name": "ProcessMessages 3406", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993650824, "dur": 26, "ph": "X", "name": "ReadAsync 3406", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993650853, "dur": 1, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993650856, "dur": 18, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993650876, "dur": 15, "ph": "X", "name": "ReadAsync 655", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993650894, "dur": 17, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993650913, "dur": 15, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993650931, "dur": 13, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993650945, "dur": 1, "ph": "X", "name": "ProcessMessages 274", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993650947, "dur": 16, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993650965, "dur": 57, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993651024, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993651047, "dur": 15, "ph": "X", "name": "ReadAsync 85", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993651065, "dur": 14, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993651081, "dur": 15, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993651099, "dur": 17, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993651118, "dur": 29, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993651150, "dur": 16, "ph": "X", "name": "ReadAsync 220", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993651170, "dur": 13, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993651185, "dur": 15, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993651202, "dur": 57, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993651262, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993651280, "dur": 16, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993651297, "dur": 16, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993651315, "dur": 52, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993651369, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993651393, "dur": 21, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993651416, "dur": 55, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993651473, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993651491, "dur": 19, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993651512, "dur": 14, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993651529, "dur": 48, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993651580, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993651604, "dur": 16, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993651622, "dur": 14, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993651639, "dur": 53, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993651694, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993651711, "dur": 16, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993651729, "dur": 14, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993651746, "dur": 53, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993651801, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993651818, "dur": 21, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993651840, "dur": 1, "ph": "X", "name": "ProcessMessages 466", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993651841, "dur": 16, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993651860, "dur": 17, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993651879, "dur": 16, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993651897, "dur": 15, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993651914, "dur": 14, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993651930, "dur": 14, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993651946, "dur": 51, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993651998, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993652014, "dur": 16, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993652032, "dur": 14, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993652048, "dur": 13, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993652062, "dur": 1, "ph": "X", "name": "ProcessMessages 62", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993652064, "dur": 46, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993652112, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993652130, "dur": 16, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993652148, "dur": 16, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993652166, "dur": 51, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993652219, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993652237, "dur": 18, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993652256, "dur": 1, "ph": "X", "name": "ProcessMessages 453", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993652257, "dur": 14, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993652273, "dur": 49, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993652325, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993652344, "dur": 16, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993652361, "dur": 1, "ph": "X", "name": "ProcessMessages 492", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993652363, "dur": 34, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993652400, "dur": 152, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993652556, "dur": 131, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993652690, "dur": 2, "ph": "X", "name": "ProcessMessages 1215", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993652693, "dur": 145, "ph": "X", "name": "ReadAsync 1215", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993652842, "dur": 2, "ph": "X", "name": "ProcessMessages 1756", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993652845, "dur": 114, "ph": "X", "name": "ReadAsync 1756", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993652963, "dur": 2, "ph": "X", "name": "ProcessMessages 864", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993652967, "dur": 65, "ph": "X", "name": "ReadAsync 864", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993653036, "dur": 1, "ph": "X", "name": "ProcessMessages 489", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993653039, "dur": 45, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993653089, "dur": 2, "ph": "X", "name": "ProcessMessages 366", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993653092, "dur": 222, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993653319, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993653322, "dur": 155, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993653481, "dur": 2, "ph": "X", "name": "ProcessMessages 1413", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993653484, "dur": 121, "ph": "X", "name": "ReadAsync 1413", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993653609, "dur": 2, "ph": "X", "name": "ProcessMessages 1861", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993653613, "dur": 73, "ph": "X", "name": "ReadAsync 1861", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993653689, "dur": 2, "ph": "X", "name": "ProcessMessages 1117", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993653692, "dur": 127, "ph": "X", "name": "ReadAsync 1117", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993653823, "dur": 1, "ph": "X", "name": "ProcessMessages 230", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993653826, "dur": 98, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993653929, "dur": 2, "ph": "X", "name": "ProcessMessages 1150", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993653933, "dur": 29, "ph": "X", "name": "ReadAsync 1150", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993653963, "dur": 1, "ph": "X", "name": "ProcessMessages 1090", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993653966, "dur": 21, "ph": "X", "name": "ReadAsync 1090", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993653988, "dur": 1, "ph": "X", "name": "ProcessMessages 527", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993653990, "dur": 40, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993654033, "dur": 72, "ph": "X", "name": "ReadAsync 86", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993654109, "dur": 20, "ph": "X", "name": "ReadAsync 644", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993654130, "dur": 1, "ph": "X", "name": "ProcessMessages 287", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993654132, "dur": 68, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993654203, "dur": 20, "ph": "X", "name": "ReadAsync 674", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993654225, "dur": 67, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993654294, "dur": 23, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993654321, "dur": 51, "ph": "X", "name": "ReadAsync 783", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993654374, "dur": 30, "ph": "X", "name": "ReadAsync 94", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993654407, "dur": 2, "ph": "X", "name": "ProcessMessages 998", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993654410, "dur": 23, "ph": "X", "name": "ReadAsync 998", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993654436, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993654459, "dur": 16, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993654477, "dur": 14, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993654493, "dur": 45, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993654541, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993654559, "dur": 19, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993654580, "dur": 15, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993654598, "dur": 54, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993654654, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993654673, "dur": 47, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993654723, "dur": 42, "ph": "X", "name": "ReadAsync 589", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993654768, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993654785, "dur": 15, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993654803, "dur": 15, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993654820, "dur": 14, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993654836, "dur": 42, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993654880, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993654898, "dur": 16, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993654917, "dur": 23, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993654942, "dur": 14, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993654957, "dur": 48, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993655008, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993655026, "dur": 15, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993655042, "dur": 1, "ph": "X", "name": "ProcessMessages 413", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993655043, "dur": 13, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993655059, "dur": 76, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993655139, "dur": 1, "ph": "X", "name": "ProcessMessages 519", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993655141, "dur": 34, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993655177, "dur": 1, "ph": "X", "name": "ProcessMessages 1242", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993655197, "dur": 13, "ph": "X", "name": "ReadAsync 1242", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993655213, "dur": 26, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993655241, "dur": 16, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993655260, "dur": 16, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993655279, "dur": 12, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993655294, "dur": 42, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993655339, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993655357, "dur": 31, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993655389, "dur": 1, "ph": "X", "name": "ProcessMessages 79", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993655390, "dur": 16, "ph": "X", "name": "ReadAsync 79", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993655409, "dur": 14, "ph": "X", "name": "ReadAsync 679", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993655426, "dur": 14, "ph": "X", "name": "ReadAsync 214", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993655443, "dur": 22, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993655468, "dur": 14, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993655484, "dur": 12, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993655498, "dur": 13, "ph": "X", "name": "ReadAsync 93", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993655512, "dur": 2, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993655515, "dur": 49, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993655568, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993655587, "dur": 17, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993655605, "dur": 1, "ph": "X", "name": "ProcessMessages 399", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993655607, "dur": 15, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993655624, "dur": 52, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993655679, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993655699, "dur": 27, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993655729, "dur": 15, "ph": "X", "name": "ReadAsync 795", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993655746, "dur": 17, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993655766, "dur": 18, "ph": "X", "name": "ReadAsync 120", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993655787, "dur": 15, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993655805, "dur": 14, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993655822, "dur": 14, "ph": "X", "name": "ReadAsync 179", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993655838, "dur": 54, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993655894, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993655912, "dur": 15, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993655930, "dur": 15, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993655946, "dur": 12, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993655961, "dur": 48, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993656010, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993656011, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993656031, "dur": 16, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993656048, "dur": 1, "ph": "X", "name": "ProcessMessages 445", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993656049, "dur": 17, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993656069, "dur": 48, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993656119, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993656138, "dur": 20, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993656161, "dur": 17, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993656180, "dur": 52, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993656234, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993656258, "dur": 15, "ph": "X", "name": "ReadAsync 619", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993656275, "dur": 14, "ph": "X", "name": "ReadAsync 482", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993656292, "dur": 49, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993656344, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993656367, "dur": 17, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993656387, "dur": 16, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993656404, "dur": 1, "ph": "X", "name": "ProcessMessages 361", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993656406, "dur": 50, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993656459, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993656485, "dur": 22, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993656509, "dur": 52, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993656564, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993656581, "dur": 71, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993656655, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993656673, "dur": 16, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993656691, "dur": 14, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993656707, "dur": 52, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993656761, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993656814, "dur": 26, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993656842, "dur": 45, "ph": "X", "name": "ReadAsync 702", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993656889, "dur": 22, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993656915, "dur": 15, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993656932, "dur": 14, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993656948, "dur": 13, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993656964, "dur": 47, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993657013, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993657036, "dur": 20, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993657058, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993657092, "dur": 12, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993657106, "dur": 45, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993657153, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993657171, "dur": 15, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993657189, "dur": 16, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993657206, "dur": 1, "ph": "X", "name": "ProcessMessages 368", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993657207, "dur": 52, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993657261, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993657285, "dur": 17, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993657306, "dur": 17, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993657325, "dur": 15, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993657342, "dur": 21, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993657366, "dur": 15, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993657383, "dur": 28, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993657413, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993657428, "dur": 49, "ph": "X", "name": "ReadAsync 222", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993657479, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993657481, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993657506, "dur": 17, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993657525, "dur": 18, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993657546, "dur": 17, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993657564, "dur": 1, "ph": "X", "name": "ProcessMessages 430", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993657566, "dur": 13, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993657582, "dur": 14, "ph": "X", "name": "ReadAsync 55", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993657597, "dur": 14, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993657614, "dur": 15, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993657631, "dur": 14, "ph": "X", "name": "ReadAsync 151", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993657646, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993657648, "dur": 57, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993657707, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993657725, "dur": 15, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993657742, "dur": 15, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993657759, "dur": 60, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993657822, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993657844, "dur": 17, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993657864, "dur": 15, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993657881, "dur": 16, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993657899, "dur": 52, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993657954, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993657987, "dur": 49, "ph": "X", "name": "ReadAsync 690", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993658037, "dur": 1, "ph": "X", "name": "ProcessMessages 805", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993658038, "dur": 16, "ph": "X", "name": "ReadAsync 805", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993658057, "dur": 43, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993658102, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993658119, "dur": 15, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993658137, "dur": 18, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993658157, "dur": 12, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993658169, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993658171, "dur": 44, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993658218, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993658235, "dur": 16, "ph": "X", "name": "ReadAsync 184", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993658254, "dur": 15, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993658271, "dur": 15, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993658288, "dur": 54, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993658344, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993658363, "dur": 16, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993658381, "dur": 15, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993658398, "dur": 51, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993658451, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993658469, "dur": 128, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993658601, "dur": 2, "ph": "X", "name": "ProcessMessages 442", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993658604, "dur": 57, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993658663, "dur": 1, "ph": "X", "name": "ProcessMessages 1892", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993658667, "dur": 17, "ph": "X", "name": "ReadAsync 1892", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993658686, "dur": 15, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993658703, "dur": 18, "ph": "X", "name": "ReadAsync 191", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993658723, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993658740, "dur": 13, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993658756, "dur": 68, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993658827, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993658844, "dur": 15, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993658861, "dur": 16, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993658879, "dur": 16, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993658897, "dur": 16, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993658916, "dur": 14, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993658932, "dur": 15, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993658949, "dur": 17, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993658968, "dur": 13, "ph": "X", "name": "ReadAsync 217", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993658984, "dur": 50, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993659036, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993659055, "dur": 14, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993659071, "dur": 18, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993659091, "dur": 18, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993659113, "dur": 18, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993659133, "dur": 15, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993659151, "dur": 14, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993659167, "dur": 19, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993659189, "dur": 60, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993659252, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993659271, "dur": 16, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993659289, "dur": 15, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993659307, "dur": 56, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993659366, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993659384, "dur": 20, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993659406, "dur": 16, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993659424, "dur": 56, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993659483, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993659502, "dur": 15, "ph": "X", "name": "ReadAsync 503", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993659519, "dur": 15, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993659536, "dur": 14, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993659552, "dur": 16, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993659571, "dur": 14, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993659587, "dur": 16, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993659605, "dur": 15, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993659622, "dur": 13, "ph": "X", "name": "ReadAsync 103", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993659636, "dur": 1, "ph": "X", "name": "ProcessMessages 134", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993659638, "dur": 52, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993659692, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993659711, "dur": 57, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993659771, "dur": 1, "ph": "X", "name": "ProcessMessages 682", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993659774, "dur": 47, "ph": "X", "name": "ReadAsync 682", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993659823, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993659844, "dur": 23, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993659869, "dur": 1, "ph": "X", "name": "ProcessMessages 436", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993659870, "dur": 97, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993659969, "dur": 15, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993659987, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993660005, "dur": 58, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993660068, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993660089, "dur": 30, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993660122, "dur": 50, "ph": "X", "name": "ReadAsync 654", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993660175, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993660195, "dur": 16, "ph": "X", "name": "ReadAsync 491", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993660213, "dur": 15, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993660230, "dur": 49, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993660282, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993660299, "dur": 21, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993660322, "dur": 15, "ph": "X", "name": "ReadAsync 716", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993660339, "dur": 52, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993660394, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993660417, "dur": 1, "ph": "X", "name": "ProcessMessages 538", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993660419, "dur": 15, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993660436, "dur": 13, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993660451, "dur": 47, "ph": "X", "name": "ReadAsync 145", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993660500, "dur": 153, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993660655, "dur": 82, "ph": "X", "name": "ReadAsync 222", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993660740, "dur": 30, "ph": "X", "name": "ReadAsync 907", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993660771, "dur": 1, "ph": "X", "name": "ProcessMessages 1128", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993660772, "dur": 30, "ph": "X", "name": "ReadAsync 1128", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993660805, "dur": 88, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993660898, "dur": 1, "ph": "X", "name": "ProcessMessages 484", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993660901, "dur": 43, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993660945, "dur": 1, "ph": "X", "name": "ProcessMessages 1964", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993660947, "dur": 14, "ph": "X", "name": "ReadAsync 1964", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993660963, "dur": 13, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993660979, "dur": 13, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993660993, "dur": 60, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993661055, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993661103, "dur": 17, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993661122, "dur": 17, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993661141, "dur": 14, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993661157, "dur": 15, "ph": "X", "name": "ReadAsync 243", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993661173, "dur": 1, "ph": "X", "name": "ProcessMessages 238", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993661175, "dur": 16, "ph": "X", "name": "ReadAsync 238", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993661193, "dur": 11, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993661206, "dur": 12, "ph": "X", "name": "ReadAsync 73", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993661221, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993661238, "dur": 55, "ph": "X", "name": "ReadAsync 222", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993661295, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993661316, "dur": 15, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993661333, "dur": 13, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993661348, "dur": 50, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993661399, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993661416, "dur": 15, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993661433, "dur": 14, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993661449, "dur": 12, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993661464, "dur": 47, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993661513, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993661536, "dur": 14, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993661552, "dur": 13, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993661567, "dur": 51, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993661621, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993661640, "dur": 17, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993661660, "dur": 15, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993661676, "dur": 48, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993661726, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993661747, "dur": 15, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993661764, "dur": 15, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993661781, "dur": 53, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993661836, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993661852, "dur": 15, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993661869, "dur": 14, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993661886, "dur": 10, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993661898, "dur": 47, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993661947, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993661966, "dur": 13, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993661981, "dur": 21, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993662004, "dur": 52, "ph": "X", "name": "ReadAsync 613", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993662058, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993662078, "dur": 16, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993662096, "dur": 13, "ph": "X", "name": "ReadAsync 480", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993662113, "dur": 12, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993662126, "dur": 43, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993662171, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993662194, "dur": 31, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993662227, "dur": 14, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993662242, "dur": 1, "ph": "X", "name": "ProcessMessages 197", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993662244, "dur": 49, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993662294, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993662314, "dur": 18, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993662335, "dur": 13, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993662350, "dur": 51, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993662403, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993662423, "dur": 13, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993662439, "dur": 15, "ph": "X", "name": "ReadAsync 159", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993662456, "dur": 13, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993662471, "dur": 48, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993662521, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993662541, "dur": 14, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993662557, "dur": 37, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993662598, "dur": 22, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993662622, "dur": 16, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993662640, "dur": 21, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993662664, "dur": 13, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993662680, "dur": 13, "ph": "X", "name": "ReadAsync 99", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993662695, "dur": 51, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993662749, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993662773, "dur": 13, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993662787, "dur": 1, "ph": "X", "name": "ProcessMessages 426", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993662789, "dur": 13, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993662804, "dur": 51, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993662858, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993662874, "dur": 14, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993662890, "dur": 17, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993662909, "dur": 13, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993662924, "dur": 95, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993663022, "dur": 70, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993663093, "dur": 1, "ph": "X", "name": "ProcessMessages 1926", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993663095, "dur": 64, "ph": "X", "name": "ReadAsync 1926", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993663161, "dur": 1, "ph": "X", "name": "ProcessMessages 772", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993663162, "dur": 16, "ph": "X", "name": "ReadAsync 772", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993663180, "dur": 53, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993663237, "dur": 1, "ph": "X", "name": "ProcessMessages 390", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993663239, "dur": 36, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993663277, "dur": 2, "ph": "X", "name": "ProcessMessages 1881", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993663280, "dur": 16, "ph": "X", "name": "ReadAsync 1881", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993663298, "dur": 14, "ph": "X", "name": "ReadAsync 182", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993663314, "dur": 13, "ph": "X", "name": "ReadAsync 185", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993663329, "dur": 46, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993663377, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993663401, "dur": 14, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993663416, "dur": 18, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993663437, "dur": 15, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993663455, "dur": 12, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993663469, "dur": 15, "ph": "X", "name": "ReadAsync 82", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993663486, "dur": 14, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993663503, "dur": 15, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993663520, "dur": 3105, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993666630, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993666633, "dur": 309, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993666946, "dur": 438, "ph": "X", "name": "ProcessMessages 10533", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993667389, "dur": 58, "ph": "X", "name": "ReadAsync 10533", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993667449, "dur": 7, "ph": "X", "name": "ProcessMessages 1136", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993667457, "dur": 29, "ph": "X", "name": "ReadAsync 1136", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993667490, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993667494, "dur": 65, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993667591, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993667594, "dur": 78, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993667675, "dur": 2, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993667678, "dur": 23, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993667712, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993667715, "dur": 22, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993667738, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993667740, "dur": 15, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993667774, "dur": 41, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993667822, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993667825, "dur": 135, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993667963, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993667965, "dur": 31, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993667998, "dur": 2, "ph": "X", "name": "ProcessMessages 336", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993668001, "dur": 49, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993668053, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993668055, "dur": 25, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993668082, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993668084, "dur": 24, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993668113, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993668132, "dur": 7, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993668145, "dur": 15, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993668165, "dur": 12, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993668182, "dur": 16, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993668200, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993668202, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993668229, "dur": 381, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993668613, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993668658, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993668678, "dur": 4, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993668691, "dur": 1486, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993670188, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993670190, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993670216, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993670223, "dur": 215, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993670443, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993670453, "dur": 164, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993670632, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993670635, "dur": 26, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993670668, "dur": 13215, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993683890, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993683898, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993683924, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993683925, "dur": 1665, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993685596, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993685599, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993685643, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993685646, "dur": 133, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993685784, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993685786, "dur": 478, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993686269, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993686276, "dur": 165, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993686445, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993686448, "dur": 106, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993686557, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993686559, "dur": 192, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993686757, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993686796, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993686798, "dur": 157, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993686964, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993686995, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993686997, "dur": 95, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993687098, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993687131, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993687133, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993687161, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993687185, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993687223, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993687226, "dur": 14, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993687243, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993687259, "dur": 50, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993687311, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993687333, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993687335, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993687358, "dur": 66, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993687429, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993687444, "dur": 590, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993688036, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993688055, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993688057, "dur": 41, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993688100, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993688127, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993688129, "dur": 17, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993688149, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993688179, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993688181, "dur": 15, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993688200, "dur": 37, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993688239, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993688257, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993688291, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993688309, "dur": 49, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993688360, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993688388, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993688405, "dur": 47, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993688456, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993688484, "dur": 14, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993688502, "dur": 47, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993688553, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993688586, "dur": 17, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993688607, "dur": 42, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993688654, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993688669, "dur": 193, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993688864, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993688893, "dur": 68, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993688964, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993688983, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993688985, "dur": 42, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993689031, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993689047, "dur": 372, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993689422, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993689448, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993689449, "dur": 148, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993689600, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993689612, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993689620, "dur": 14, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993689672, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993689690, "dur": 33, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993689728, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993689762, "dur": 32, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993689797, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993689817, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993689838, "dur": 67, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993689908, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993689935, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993689937, "dur": 193, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993690132, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993690166, "dur": 17, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993690187, "dur": 34, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993690224, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993690242, "dur": 12, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993690257, "dur": 161, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993690420, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993690438, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993690465, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993690487, "dur": 4, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993690491, "dur": 123, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993690617, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993690677, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993690679, "dur": 21, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993690704, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993690724, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993690725, "dur": 123, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993690853, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993690855, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993690875, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993690905, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993690938, "dur": 83, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993691023, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993691040, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993691042, "dur": 177, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993691221, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993691244, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993691246, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993691263, "dur": 38, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993691305, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993691323, "dur": 17, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993691342, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993691363, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993691412, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993691428, "dur": 42, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993691472, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993691492, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993691494, "dur": 39, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993691536, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993691571, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993691573, "dur": 14, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993691589, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993691610, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993691641, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993691660, "dur": 86, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993691770, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993691790, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993691815, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993691856, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993691858, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993691885, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993691902, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993691931, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993691964, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993691967, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993691984, "dur": 329, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993692318, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993692343, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993692377, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993692397, "dur": 70, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993692469, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993692488, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993692490, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993692513, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993692536, "dur": 65, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993692606, "dur": 10, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993692616, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993692632, "dur": 89, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993692727, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993692743, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993692769, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993692779, "dur": 9, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993692789, "dur": 48, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993692842, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993692865, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993692866, "dur": 37, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993692906, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993692924, "dur": 51, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993692977, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993693003, "dur": 76, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993693081, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993693111, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993693131, "dur": 102, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993693237, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993693250, "dur": 6, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993693257, "dur": 29, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993693291, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993693310, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993693324, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993693354, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993693367, "dur": 85, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993693454, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993693475, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993693477, "dur": 65, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993693547, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993693571, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993693572, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993693588, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993693620, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993693622, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993693640, "dur": 129, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993693773, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993693786, "dur": 107, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993693895, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993693914, "dur": 11, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993693936, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993693958, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993693959, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993693984, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993693998, "dur": 206, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993694215, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993694235, "dur": 16, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993694261, "dur": 72, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993694337, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993694358, "dur": 81, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993694442, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993694467, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993694484, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993694509, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993694523, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993694547, "dur": 48, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993694597, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993694599, "dur": 11, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993694612, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993694644, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993694645, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993694663, "dur": 14, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993694679, "dur": 138, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993694821, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993694835, "dur": 46, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993694885, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993694899, "dur": 39, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993694940, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993694965, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993694967, "dur": 88, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993695057, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993695077, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993695079, "dur": 95, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993695176, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993695196, "dur": 40, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993695238, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993695256, "dur": 53, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993695311, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993695342, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993695344, "dur": 179, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993695526, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993695529, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993695560, "dur": 91, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993695653, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993695663, "dur": 48, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993695756, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993695759, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993695775, "dur": 43, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993695820, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993695822, "dur": 11, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993695836, "dur": 11, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993695851, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993695867, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993695905, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993695907, "dur": 52, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993695964, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993696011, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993696026, "dur": 61, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993696095, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993696151, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993696153, "dur": 73, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993696230, "dur": 22, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993696253, "dur": 101, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993696356, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993696386, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993696388, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993696418, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993696454, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993696471, "dur": 422, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993696898, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993696932, "dur": 30, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993696965, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993696966, "dur": 134, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993697111, "dur": 99, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993697214, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993697216, "dur": 115, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993697335, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993697337, "dur": 130, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993697471, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993697473, "dur": 27, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993697503, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993697506, "dur": 44, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993697555, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993697604, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993697640, "dur": 793, "ph": "X", "name": "ProcessMessages 24", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993698437, "dur": 32, "ph": "X", "name": "ReadAsync 24", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993698472, "dur": 2, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993698474, "dur": 163, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993698642, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993698655, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993698679, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993698681, "dur": 18, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993698702, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993698704, "dur": 29, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993698735, "dur": 89, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993698833, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993698836, "dur": 69, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993698909, "dur": 267, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993699181, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993699183, "dur": 138, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993699325, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993699330, "dur": 111, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993699445, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993699457, "dur": 110, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993699599, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993699601, "dur": 60, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993699665, "dur": 100, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993699769, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993699771, "dur": 387, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993700195, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993700239, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993700242, "dur": 67, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993700315, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993700343, "dur": 119, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993700467, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993700468, "dur": 130, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993700602, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993700604, "dur": 328, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993700937, "dur": 202, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993701143, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993701144, "dur": 42, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993701190, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993701192, "dur": 97, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993701292, "dur": 587, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993701884, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993701899, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993701902, "dur": 60281, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993762194, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993762198, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993762252, "dur": 2017, "ph": "X", "name": "ProcessMessages 206", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993764275, "dur": 6021, "ph": "X", "name": "ReadAsync 206", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993770306, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993770310, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993770343, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993770345, "dur": 98, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993770450, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993770453, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993770493, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993770495, "dur": 495, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993770994, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993770995, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993771031, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993771033, "dur": 439, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993771476, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993771510, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993771513, "dur": 118, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993771633, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993771670, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993771672, "dur": 68, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993771743, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993771772, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993771775, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993771801, "dur": 121, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993771928, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993771955, "dur": 64, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993772024, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993772051, "dur": 27, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993772082, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993772105, "dur": 471, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993772581, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993772604, "dur": 536, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993773142, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993773174, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993773178, "dur": 466, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993773646, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993773673, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993773675, "dur": 417, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993774094, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993774125, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993774127, "dur": 300, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993774429, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993774458, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993774460, "dur": 305, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993774770, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993774809, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993774812, "dur": 89, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993774904, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993774936, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993774938, "dur": 167, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993775108, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993775135, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993775137, "dur": 370, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993775509, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993775534, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993775536, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993775571, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993775574, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993775601, "dur": 1316, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993776922, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993776955, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993776957, "dur": 990, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993777950, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993777976, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993777978, "dur": 437, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993778419, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993778445, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993778449, "dur": 29, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993778481, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993778506, "dur": 147, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993778655, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993778680, "dur": 87, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993778769, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993778793, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993778796, "dur": 79, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993778877, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993778911, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993778913, "dur": 329, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993779246, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993779283, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993779286, "dur": 285, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993779574, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993779605, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993779608, "dur": 1220, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993780832, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993780834, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993780883, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993780886, "dur": 126, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993781014, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993781049, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993781051, "dur": 63, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993781116, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993781149, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993781152, "dur": 980, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993782135, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993782170, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993782173, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993782201, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993782238, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993782241, "dur": 205, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993782447, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993782478, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993782481, "dur": 69, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993782553, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993782588, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993782591, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993782616, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993782641, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993782644, "dur": 400, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993783046, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993783078, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993783081, "dur": 109, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993783192, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993783220, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993783222, "dur": 143, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993783367, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993783396, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993783398, "dur": 159, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993783559, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993783587, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993783589, "dur": 653, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993784245, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993784278, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993784281, "dur": 904, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993785188, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993785222, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993785225, "dur": 236, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993785464, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993785496, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993785499, "dur": 62, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993785563, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993785592, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993785595, "dur": 112, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993785709, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993785751, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993785754, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993785786, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993785788, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993785827, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993785829, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993785857, "dur": 710, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993786573, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993786575, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993786612, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993786615, "dur": 608, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993787227, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993787230, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993787266, "dur": 278, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993787548, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993787550, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993787579, "dur": 74, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993787657, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993787660, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993787686, "dur": 1226, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993788917, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993788920, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993788949, "dur": 141, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993789094, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993789117, "dur": 75, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993789196, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993789217, "dur": 144, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993789365, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993789391, "dur": 50, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993789446, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993789473, "dur": 101, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993789578, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993789581, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993789605, "dur": 385, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993789995, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993790021, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993790048, "dur": 248, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993790298, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993790327, "dur": 142, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993790471, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993790498, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993790500, "dur": 642, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993791145, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993791147, "dur": 90, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993791243, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993791245, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993791275, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993791277, "dur": 362, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993791644, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993791666, "dur": 278, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993791949, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993791971, "dur": 576, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993792551, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993792574, "dur": 1009, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993793587, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993793590, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993793625, "dur": 36, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993793666, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993793668, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993793693, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993793726, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993793749, "dur": 32, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993793786, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993793818, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993793820, "dur": 30, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993793853, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993793855, "dur": 23, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993793879, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993793882, "dur": 22, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993793908, "dur": 50, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993793962, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993793991, "dur": 114, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993794109, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993794135, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993794159, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993794185, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993794187, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993794212, "dur": 22, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993794236, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993794238, "dur": 19, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993794260, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993794289, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993794311, "dur": 23, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993794338, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993794368, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993794370, "dur": 55, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993794428, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993794430, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993794455, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993794456, "dur": 37, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993794496, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993794498, "dur": 24, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993794524, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993794526, "dur": 69, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993794599, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993794602, "dur": 27, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993794631, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993794632, "dur": 30, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993794666, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993794668, "dur": 25, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993794697, "dur": 28, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993794728, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993794730, "dur": 22, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993794755, "dur": 24, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993794782, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993794784, "dur": 24, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993794809, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993794811, "dur": 23, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993794836, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993794838, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993794861, "dur": 35, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993794899, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993794900, "dur": 26, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993794927, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993794929, "dur": 27, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993794959, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993794962, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993794987, "dur": 21, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993795011, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993795012, "dur": 31, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993795046, "dur": 26, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993795075, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993795077, "dur": 32, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993795111, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993795113, "dur": 30, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993795147, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993795149, "dur": 28, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993795179, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993795181, "dur": 29, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993795213, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993795216, "dur": 26, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993795245, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993795247, "dur": 26, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993795276, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993795279, "dur": 27, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993795307, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993795309, "dur": 25, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993795337, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993795340, "dur": 29, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993795370, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993795372, "dur": 34, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993795410, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993795413, "dur": 41, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993795458, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993795460, "dur": 54, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993795520, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993795557, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993795559, "dur": 31, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993795595, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993795598, "dur": 162, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993795765, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993795800, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993795803, "dur": 25, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993795832, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993795838, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993795869, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993795871, "dur": 72662, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993868540, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993868543, "dur": 163, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993868707, "dur": 14, "ph": "X", "name": "ProcessMessages 367", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547993868722, "dur": 450836, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547994319567, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547994319570, "dur": 95, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547994319667, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547994319672, "dur": 129239, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547994448920, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547994448923, "dur": 77, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547994449001, "dur": 20, "ph": "X", "name": "ProcessMessages 602", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547994449022, "dur": 13620, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547994462650, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547994462653, "dur": 126, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547994462788, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547994462792, "dur": 1819, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547994464616, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547994464618, "dur": 98, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547994464717, "dur": 16, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547994464734, "dur": 462505, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547994927252, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547994927259, "dur": 84, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547994927349, "dur": 5, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547994927357, "dur": 1632, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547994928995, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547994928997, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547994929023, "dur": 16, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547994929040, "dur": 40, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547994929084, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547994929086, "dur": 145, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547994929234, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547994929236, "dur": 448, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547994929688, "dur": 66, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547994929759, "dur": 575, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 13804, "tid": 12884901888, "ts": 1755547994930339, "dur": 11716, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 13804, "tid": 164368, "ts": 1755547994956985, "dur": 1794, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 13804, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 13804, "tid": 8589934592, "ts": 1755547993625929, "dur": 146904, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 13804, "tid": 8589934592, "ts": 1755547993772836, "dur": 5, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 13804, "tid": 8589934592, "ts": 1755547993772843, "dur": 1396, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 13804, "tid": 164368, "ts": 1755547994958781, "dur": 4, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 13804, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 13804, "tid": 4294967296, "ts": 1755547993609644, "dur": 1334788, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 13804, "tid": 4294967296, "ts": 1755547993612580, "dur": 6297, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 13804, "tid": 4294967296, "ts": 1755547994944479, "dur": 4454, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 13804, "tid": 4294967296, "ts": 1755547994947071, "dur": 176, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 13804, "tid": 4294967296, "ts": 1755547994948996, "dur": 10, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 13804, "tid": 164368, "ts": 1755547994958788, "dur": 9, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1755547993635372, "dur": 1747, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1755547993637129, "dur": 1159, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1755547993638461, "dur": 110, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1755547993638571, "dur": 435, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1755547993639321, "dur": 90, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_8BA1F79F85B3575D.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1755547993639827, "dur": 217, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_A5F21C0EBB6C174C.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1755547993640739, "dur": 1561, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.APIComparison.Framework.dll_795DFBA713DFDA95.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1755547993642521, "dur": 104, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TestRunner.ref.dll_193EC4CE382CBFB3.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1755547993643051, "dur": 89, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1755547993645754, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Mirror.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1755547993648407, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1755547993654620, "dur": 82, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Notifications.Android.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1755547993654719, "dur": 82, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Notifications.iOS.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1755547993661617, "dur": 123, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1755547993662268, "dur": 117, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1755547993639028, "dur": 26529, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1755547993665576, "dur": 1265420, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1755547994930997, "dur": 196, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1755547994931335, "dur": 73, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1755547994931433, "dur": 1788, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1755547993639585, "dur": 26237, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755547993665831, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_18C55C75F47E4EB6.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1755547993666200, "dur": 166, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_48419839367BA1E6.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1755547993666368, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_AE2A3D5B7498DE25.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1755547993667568, "dur": 460, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.iOS.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1755547993668358, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755547993668465, "dur": 338, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1755547993668804, "dur": 449, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755547993669698, "dur": 2215, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755547993671924, "dur": 624, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755547993672548, "dur": 907, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755547993673456, "dur": 657, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755547993674114, "dur": 679, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755547993674830, "dur": 645, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@39bda2df468a\\Editor\\Camera\\UniversalRenderPipelineCameraUI.Drawers.cs"}}, {"pid": 12345, "tid": 1, "ts": 1755547993674794, "dur": 1317, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755547993676112, "dur": 682, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755547993676794, "dur": 687, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755547993677481, "dur": 659, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755547993678140, "dur": 690, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755547993678830, "dur": 660, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755547993679490, "dur": 666, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755547993680157, "dur": 666, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755547993680823, "dur": 684, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755547993681508, "dur": 693, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755547993682201, "dur": 528, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755547993682729, "dur": 471, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755547993683200, "dur": 482, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755547993683852, "dur": 703, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.package-validation-suite@536239bd7458\\Editor\\ValidationSuite\\ValidationTests\\PackageUnityVersionValidation.cs"}}, {"pid": 12345, "tid": 1, "ts": 1755547993683682, "dur": 1269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755547993684952, "dur": 676, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755547993685632, "dur": 705, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755547993686338, "dur": 717, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755547993687102, "dur": 63, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755547993687169, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755547993687232, "dur": 788, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755547993688020, "dur": 448, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755547993688469, "dur": 262, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Mirror.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1755547993688731, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755547993688799, "dur": 240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1755547993689082, "dur": 847, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1755547993689930, "dur": 302, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755547993690242, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755547993690372, "dur": 320, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1755547993690737, "dur": 1042, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Mirror.Components.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1755547993691780, "dur": 603, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755547993692389, "dur": 1190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/kcp2k.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1755547993693580, "dur": 494, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755547993694082, "dur": 1523, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Mirror.Transports.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1755547993695605, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755547993695700, "dur": 261, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1755547993695995, "dur": 1757, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1755547993697752, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755547993697912, "dur": 1330, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755547993699242, "dur": 2637, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755547993701884, "dur": 283, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1755547993702169, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755547993702234, "dur": 66885, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755547993769121, "dur": 3958, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1755547993773080, "dur": 1255, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755547993774342, "dur": 3905, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1755547993778249, "dur": 420, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755547993778682, "dur": 3761, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1755547993782444, "dur": 1439, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755547993783890, "dur": 3498, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1755547993787389, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755547993787527, "dur": 3445, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1755547993790973, "dur": 1260, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755547993792241, "dur": 3825, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Playmode.Common.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1755547993796066, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755547993796144, "dur": 94, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Playmode.Common.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1755547993796266, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755547993796324, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Multiplayer.Playmode.Common.Editor.dll"}}, {"pid": 12345, "tid": 1, "ts": 1755547993796400, "dur": 251, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755547993796667, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755547993796907, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755547993797133, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/kcp2k.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1755547993797191, "dur": 1133895, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755547993639249, "dur": 26336, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755547993665612, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755547993665712, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_5ECB2C4C110CA6E5.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1755547993665968, "dur": 262, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755547993666338, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755547993666497, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_97F9292D53E5A600.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1755547993667317, "dur": 881, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755547993668236, "dur": 245, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755547993668481, "dur": 394, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Mirror.Components.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1755547993668877, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755547993669209, "dur": 529, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1755547993669764, "dur": 2626, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755547993672395, "dur": 698, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755547993673094, "dur": 514, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755547993673608, "dur": 659, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755547993674268, "dur": 1397, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755547993675666, "dur": 663, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755547993676330, "dur": 647, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755547993676977, "dur": 657, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755547993677634, "dur": 657, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755547993678291, "dur": 651, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755547993678942, "dur": 658, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755547993679600, "dur": 725, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755547993680325, "dur": 740, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755547993681065, "dur": 710, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755547993681776, "dur": 1094, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755547993682871, "dur": 818, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755547993683689, "dur": 681, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755547993684371, "dur": 695, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755547993685067, "dur": 677, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755547993685745, "dur": 766, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755547993686512, "dur": 758, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755547993687270, "dur": 757, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755547993688028, "dur": 496, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755547993688526, "dur": 2055, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1755547993690636, "dur": 760, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1755547993691397, "dur": 546, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755547993691992, "dur": 265, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1755547993692257, "dur": 904, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755547993693165, "dur": 1253, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1755547993694419, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755547993694601, "dur": 378, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Mirror.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1755547993694980, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755547993695052, "dur": 1049, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Mirror.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1755547993696102, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755547993696315, "dur": 335, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1755547993696650, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755547993696724, "dur": 597, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1755547993697322, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755547993697464, "dur": 192, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755547993697661, "dur": 150, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755547993697812, "dur": 289, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1755547993698147, "dur": 602, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1755547993698749, "dur": 330, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755547993699103, "dur": 138, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755547993699242, "dur": 2785, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755547993702027, "dur": 66914, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755547993768962, "dur": 5114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Mirror.Components.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1755547993774077, "dur": 818, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755547993774907, "dur": 5294, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1755547993780202, "dur": 211, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755547993780424, "dur": 3536, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Playmode.WorkflowUI.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1755547993783961, "dur": 242, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755547993784214, "dur": 3715, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PackageValidationSuite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1755547993787930, "dur": 371, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755547993788343, "dur": 3280, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1755547993791624, "dur": 432, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755547993792120, "dur": 3727, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1755547993795848, "dur": 367, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755547993796474, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755547993796640, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Multiplayer.Playmode.Workflow.Editor.dll"}}, {"pid": 12345, "tid": 2, "ts": 1755547993796710, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755547993797169, "dur": 1133937, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755547993639433, "dur": 26286, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755547993665723, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_B67EF2BB05ABACA5.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1755547993666339, "dur": 1234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_10364B2612418FBF.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1755547993668323, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1755547993668427, "dur": 257, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1755547993668754, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755547993668932, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755547993669762, "dur": 2433, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755547993672208, "dur": 929, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755547993673137, "dur": 763, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755547993673900, "dur": 685, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755547993674816, "dur": 741, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@39bda2df468a\\Editor\\ShaderGUI\\ShaderGraphUnlitGUI.cs"}}, {"pid": 12345, "tid": 3, "ts": 1755547993674585, "dur": 1481, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755547993676066, "dur": 765, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755547993676832, "dur": 693, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755547993677525, "dur": 686, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755547993678212, "dur": 654, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755547993678866, "dur": 679, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755547993679545, "dur": 709, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755547993680254, "dur": 655, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755547993680910, "dur": 710, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755547993681621, "dur": 855, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755547993682477, "dur": 689, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755547993683166, "dur": 682, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755547993683848, "dur": 690, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755547993684539, "dur": 671, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755547993685210, "dur": 696, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755547993685906, "dur": 679, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755547993686586, "dur": 858, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755547993687444, "dur": 575, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755547993688020, "dur": 544, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755547993688565, "dur": 335, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1755547993688901, "dur": 1313, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755547993690228, "dur": 1800, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1755547993692028, "dur": 976, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755547993693012, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Timeline.ref.dll_621CDDF9C514DF8F.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1755547993693071, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755547993693233, "dur": 343, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1755547993693576, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755547993693638, "dur": 1673, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1755547993695312, "dur": 348, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755547993695669, "dur": 678, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1755547993696387, "dur": 1382, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1755547993697770, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755547993697913, "dur": 1323, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755547993699236, "dur": 2795, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755547993702031, "dur": 66929, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755547993768963, "dur": 4336, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Mirror.Transports.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1755547993773300, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755547993773397, "dur": 3600, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1755547993776998, "dur": 270, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755547993777279, "dur": 3268, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Notifications.Android.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1755547993780548, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755547993780644, "dur": 3622, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1755547993784267, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755547993784369, "dur": 2887, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InternalAPIEditorBridge.022.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1755547993787257, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755547993787329, "dur": 3571, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1755547993790901, "dur": 289, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755547993791196, "dur": 3274, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1755547993794470, "dur": 1086, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755547993795567, "dur": 385, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755547993795993, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755547993796101, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755547993796303, "dur": 116, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/com.unity.cinemachine.editor.dll"}}, {"pid": 12345, "tid": 3, "ts": 1755547993796424, "dur": 50, "ph": "X", "name": "CheckPristineOutputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.ShaderGraph.Editor.dll"}}, {"pid": 12345, "tid": 3, "ts": 1755547993796474, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755547993796557, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755547993796714, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755547993796870, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755547993796953, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755547993797155, "dur": 365, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755547993797520, "dur": 1133595, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755547993639322, "dur": 26277, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755547993665613, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755547993665717, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_08B2EE5ACE61D9A1.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1755547993666348, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755547993666506, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_0B408E8BC3EE7994.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1755547993667369, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755547993667559, "dur": 124, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.Android.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1755547993668431, "dur": 261, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1755547993668741, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755547993668873, "dur": 467, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755547993669510, "dur": 932, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10397561839769426034.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1755547993670443, "dur": 648, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755547993671091, "dur": 567, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755547993671870, "dur": 751, "ph": "X", "name": "File", "args": {"detail": "Assets\\Mirror\\Hosting\\Edgegap\\Models\\SDK\\MetricsResponse.cs"}}, {"pid": 12345, "tid": 4, "ts": 1755547993671658, "dur": 1356, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755547993673014, "dur": 747, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755547993673762, "dur": 495, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755547993674258, "dur": 471, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755547993674829, "dur": 621, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@39bda2df468a\\Editor\\Converter\\Converters.cs"}}, {"pid": 12345, "tid": 4, "ts": 1755547993674729, "dur": 1518, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755547993676248, "dur": 668, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755547993676916, "dur": 671, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755547993677587, "dur": 672, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755547993678260, "dur": 656, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755547993678916, "dur": 676, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755547993679593, "dur": 689, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755547993680282, "dur": 645, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755547993680927, "dur": 723, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755547993681650, "dur": 771, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755547993682421, "dur": 688, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755547993683299, "dur": 568, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.multiplayer.playmode@7f4b34d911b7\\VirtualProjects\\Editor\\Utility\\LogCounts.cs"}}, {"pid": 12345, "tid": 4, "ts": 1755547993683110, "dur": 1089, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755547993684497, "dur": 1276, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@7fe8299111a7\\InputSystem\\Plugins\\PlayerInput\\PlayerInputEditor.cs"}}, {"pid": 12345, "tid": 4, "ts": 1755547993684199, "dur": 1740, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755547993685939, "dur": 501, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755547993686441, "dur": 475, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755547993686999, "dur": 82, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755547993687084, "dur": 108, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755547993687192, "dur": 833, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755547993688026, "dur": 495, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755547993688523, "dur": 382, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Playmode.Common.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1755547993688906, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755547993688973, "dur": 716, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Playmode.Common.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1755547993689690, "dur": 266, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755547993690014, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/kcp2k.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1755547993690252, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755547993690410, "dur": 288, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Mirror.Transports.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1755547993690745, "dur": 1703, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1755547993692449, "dur": 340, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755547993692798, "dur": 236, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1755547993693070, "dur": 1498, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1755547993694569, "dur": 1702, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755547993696427, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1755547993696550, "dur": 1234, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755547993697787, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1755547993697933, "dur": 595, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1755547993698530, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755547993698696, "dur": 489, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755547993699187, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1755547993699374, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755547993699431, "dur": 596, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1755547993700028, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755547993700174, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Simulator.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1755547993700390, "dur": 511, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Simulator.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1755547993700902, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755547993701032, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Simulator.Extension.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1755547993701131, "dur": 253, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Simulator.Extension.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1755547993701444, "dur": 558, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755547993702002, "dur": 66929, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755547993768932, "dur": 4659, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1755547993773592, "dur": 238, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755547993773846, "dur": 6247, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Notifications.Unified.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1755547993780094, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755547993780189, "dur": 3528, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1755547993783718, "dur": 1075, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755547993784805, "dur": 3614, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1755547993788421, "dur": 3367, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755547993791814, "dur": 4014, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/EncryptionTransportEditor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1755547993795828, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755547993796137, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Timeline.dll"}}, {"pid": 12345, "tid": 4, "ts": 1755547993796199, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755547993796273, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755547993796616, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755547993796764, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755547993797167, "dur": 1133940, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755547993639480, "dur": 26245, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755547993665733, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_CBC203599E566618.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1755547993666356, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_FC9FF88BB6DDE854.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1755547993666550, "dur": 1054, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_D6691EB725D4E210.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1755547993667680, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1755547993667837, "dur": 19168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1755547993687007, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755547993687225, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1755547993687373, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755547993687431, "dur": 483, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1755547993688016, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1755547993688098, "dur": 273, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1755547993688465, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1755547993688546, "dur": 2235, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755547993690794, "dur": 1697, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1755547993692491, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755547993692592, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755547993692673, "dur": 332, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1755547993693006, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755547993693095, "dur": 1211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1755547993694307, "dur": 585, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755547993694907, "dur": 435, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755547993695363, "dur": 267, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1755547993695671, "dur": 1198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1755547993696870, "dur": 208, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755547993697094, "dur": 1572, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 5, "ts": 1755547993698713, "dur": 185, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755547993699438, "dur": 64469, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 5, "ts": 1755547993768931, "dur": 2729, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1755547993771661, "dur": 538, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755547993772209, "dur": 2965, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1755547993775176, "dur": 224, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755547993775412, "dur": 3158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Playmode.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1755547993778571, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755547993778686, "dur": 3470, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1755547993782157, "dur": 713, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755547993782878, "dur": 2629, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1755547993785508, "dur": 1959, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755547993787474, "dur": 3778, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Notifications.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1755547993791252, "dur": 2452, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755547993793711, "dur": 3035, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1755547993796747, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755547993796841, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755547993797186, "dur": 1133805, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755547993639570, "dur": 26238, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755547993665818, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_A8EE771E29308445.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1755547993665936, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_F08C4456CD2BF651.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1755547993666341, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Gradle.dll_3EC188A9A2685E71.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1755547993667502, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1755547993668428, "dur": 317, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.PlayMode.Scenarios.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1755547993668748, "dur": 460, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755547993669209, "dur": 778, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1755547993669988, "dur": 892, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755547993670881, "dur": 1029, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755547993671910, "dur": 597, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755547993672507, "dur": 791, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755547993673298, "dur": 664, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755547993673962, "dur": 679, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755547993674826, "dur": 646, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@39bda2df468a\\Editor\\RendererFeatures\\NewRendererFeatureDropdownItem.cs"}}, {"pid": 12345, "tid": 6, "ts": 1755547993675473, "dur": 653, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@39bda2df468a\\Editor\\RendererFeatures\\NewPostProcessTemplateDropdownItems.cs"}}, {"pid": 12345, "tid": 6, "ts": 1755547993676496, "dur": 736, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@39bda2df468a\\Editor\\Overrides\\ColorLookupEditor.cs"}}, {"pid": 12345, "tid": 6, "ts": 1755547993674642, "dur": 2661, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755547993677304, "dur": 699, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755547993678004, "dur": 675, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755547993678679, "dur": 691, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755547993679370, "dur": 692, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755547993680062, "dur": 710, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755547993680772, "dur": 629, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755547993681402, "dur": 696, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755547993682098, "dur": 731, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755547993682829, "dur": 694, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755547993683524, "dur": 633, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755547993684158, "dur": 713, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755547993684872, "dur": 667, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755547993685540, "dur": 680, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755547993686220, "dur": 797, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755547993687085, "dur": 101, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755547993687186, "dur": 842, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755547993688028, "dur": 501, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755547993688530, "dur": 367, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.PlayMode.Editor.Bridge.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1755547993688898, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755547993688983, "dur": 871, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.PlayMode.Editor.Bridge.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1755547993689855, "dur": 203, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755547993690073, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755547993690135, "dur": 231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Telepathy.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1755547993690418, "dur": 1878, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Telepathy.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1755547993692297, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755547993692411, "dur": 1188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Mirror.Components.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1755547993693599, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755547993693732, "dur": 513, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1755547993694294, "dur": 678, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.Unified.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1755547993694972, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755547993695122, "dur": 2251, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Mirror.Authenticators.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1755547993697374, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755547993697482, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755547993697647, "dur": 166, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755547993697814, "dur": 352, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1755547993698218, "dur": 681, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1755547993698899, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755547993699016, "dur": 210, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755547993699226, "dur": 903, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755547993700131, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1755547993700313, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755547993700378, "dur": 379, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1755547993700758, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755547993700866, "dur": 1100, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755547993701967, "dur": 207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1755547993702223, "dur": 69740, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755547993771965, "dur": 3781, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AdaptivePerformance.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1755547993775747, "dur": 442, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755547993779597, "dur": 383, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 6, "ts": 1755547993779981, "dur": 1609, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 6, "ts": 1755547993781590, "dur": 124, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Tools/Compilation/Unity.ILPP.Trigger"}}, {"pid": 12345, "tid": 6, "ts": 1755547993776213, "dur": 5502, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755547993781716, "dur": 3500, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Mirror.CompilerSymbols.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1755547993785222, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755547993785323, "dur": 3035, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Edgegap.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1755547993788358, "dur": 2971, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755547993791336, "dur": 4103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1755547993795441, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755547993795635, "dur": 229, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755547993796053, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755547993796125, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Multiplayer.Playmode.dll"}}, {"pid": 12345, "tid": 6, "ts": 1755547993796233, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755547993796587, "dur": 88, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Mobile.AndroidLogcat.Editor.dll"}}, {"pid": 12345, "tid": 6, "ts": 1755547993796692, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755547993797144, "dur": 119, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755547993797287, "dur": 1133614, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755547993639540, "dur": 26227, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755547993665783, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_FEB99ECBF3C51413.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1755547993666353, "dur": 264, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755547993666878, "dur": 502, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755547993667566, "dur": 655, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1755547993668232, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755547993668305, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755547993668427, "dur": 254, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1755547993668741, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755547993669192, "dur": 345, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEditorBridge.022.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1755547993669762, "dur": 2504, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755547993672272, "dur": 658, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755547993672930, "dur": 814, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755547993673745, "dur": 718, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755547993674463, "dur": 1393, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755547993675857, "dur": 694, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755547993676552, "dur": 654, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755547993677206, "dur": 660, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755547993677867, "dur": 666, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755547993678534, "dur": 679, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755547993679213, "dur": 689, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755547993679902, "dur": 661, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755547993680563, "dur": 740, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755547993681303, "dur": 694, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755547993681997, "dur": 574, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755547993682571, "dur": 499, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755547993683071, "dur": 490, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755547993683561, "dur": 456, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755547993684017, "dur": 469, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755547993684486, "dur": 523, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755547993685009, "dur": 480, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755547993685489, "dur": 467, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755547993685956, "dur": 154, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755547993686110, "dur": 513, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755547993686624, "dur": 827, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755547993687451, "dur": 570, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755547993688021, "dur": 505, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755547993688528, "dur": 462, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1755547993688990, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755547993689098, "dur": 1051, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1755547993690150, "dur": 1354, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755547993691569, "dur": 308, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.Unified.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1755547993691947, "dur": 250, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1755547993692229, "dur": 209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mobile.AndroidLogcat.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1755547993692473, "dur": 2707, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1755547993695228, "dur": 1448, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1755547993696677, "dur": 265, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755547993696967, "dur": 318, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755547993697292, "dur": 627, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Playmode.WorkflowUI.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1755547993697964, "dur": 1267, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755547993699231, "dur": 2801, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755547993702033, "dur": 66916, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755547993768953, "dur": 7773, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1755547993776727, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755547993776881, "dur": 3503, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1755547993780385, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755547993780540, "dur": 3532, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.ConversionSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1755547993784073, "dur": 239, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755547993784324, "dur": 4667, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1755547993788992, "dur": 294, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755547993789297, "dur": 3505, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1755547993792803, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755547993792901, "dur": 3918, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Playmode.VirtualProjects.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1755547993797127, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Burst.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1755547993797203, "dur": 1133756, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755547993639745, "dur": 26112, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755547993665876, "dur": 571, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_82AFBD6736063513.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1755547993667066, "dur": 525, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755547993668475, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755547993668530, "dur": 362, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1755547993669187, "dur": 337, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1755547993669708, "dur": 660, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755547993670372, "dur": 471, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755547993670843, "dur": 340, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755547993671184, "dur": 282, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755547993671466, "dur": 358, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755547993671841, "dur": 722, "ph": "X", "name": "File", "args": {"detail": "Assets\\Mirror\\Hosting\\Edgegap\\Models\\SDK\\DeploymentLocation.cs"}}, {"pid": 12345, "tid": 8, "ts": 1755547993671824, "dur": 1303, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755547993673127, "dur": 701, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755547993673828, "dur": 661, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755547993674810, "dur": 639, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@39bda2df468a\\Editor\\VFXGraph\\VFXURPLitMeshOutput.cs"}}, {"pid": 12345, "tid": 8, "ts": 1755547993674489, "dur": 1257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755547993675746, "dur": 478, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755547993676224, "dur": 474, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755547993676698, "dur": 466, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755547993677455, "dur": 1139, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@5c8f96f445ce\\Editor\\Data\\Graphs\\CubemapMaterialSlot.cs"}}, {"pid": 12345, "tid": 8, "ts": 1755547993677164, "dur": 1601, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755547993678765, "dur": 471, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755547993679236, "dur": 470, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755547993679706, "dur": 573, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755547993680280, "dur": 451, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755547993680731, "dur": 469, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755547993681200, "dur": 496, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755547993681696, "dur": 515, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755547993682211, "dur": 713, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755547993682925, "dur": 640, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755547993683566, "dur": 593, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755547993684159, "dur": 476, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755547993684636, "dur": 473, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755547993685109, "dur": 463, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755547993685573, "dur": 491, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755547993686065, "dur": 553, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755547993686618, "dur": 698, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755547993687317, "dur": 676, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755547993687997, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755547993688058, "dur": 473, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755547993688532, "dur": 354, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1755547993688914, "dur": 592, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1755547993689506, "dur": 293, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755547993689807, "dur": 604, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755547993690417, "dur": 314, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1755547993690795, "dur": 515, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1755547993691311, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755547993691404, "dur": 519, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1755547993691928, "dur": 1057, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755547993692992, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1755547993693179, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755547993693264, "dur": 759, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1755547993694024, "dur": 530, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755547993694604, "dur": 216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Mirror.Examples.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1755547993694856, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/EncryptionTransportEditor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1755547993695017, "dur": 952, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1755547993695969, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755547993696112, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755547993696240, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755547993696441, "dur": 358, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1755547993696838, "dur": 683, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1755547993697522, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755547993697643, "dur": 188, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755547993697831, "dur": 1445, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755547993699277, "dur": 2703, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755547993701980, "dur": 66953, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755547993768936, "dur": 3570, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1755547993772507, "dur": 1267, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755547993773782, "dur": 2672, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Playmode.Common.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1755547993776455, "dur": 202, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755547993776668, "dur": 3481, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Notifications.iOS.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1755547993780150, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755547993780251, "dur": 2413, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.PlayMode.Editor.Bridge.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1755547993782665, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755547993782775, "dur": 4351, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1755547993787127, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755547993787227, "dur": 5095, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1755547993792323, "dur": 660, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755547993792990, "dur": 4296, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AdaptivePerformance.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1755547993797337, "dur": 1133554, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755547993640085, "dur": 26298, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755547993666389, "dur": 713, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_72AEB35BFEDD1B08.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1755547993667209, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1755547993667347, "dur": 1106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755547993668453, "dur": 334, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.PlayMode.Editor.Bridge.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1755547993668788, "dur": 451, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755547993669499, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755547993669966, "dur": 709, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755547993670676, "dur": 781, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755547993671457, "dur": 599, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755547993672056, "dur": 698, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755547993672754, "dur": 678, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755547993673965, "dur": 1567, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ide.rider@7d01a2258732\\Rider\\Editor\\ProjectGeneration\\ProjectGeneration.cs"}}, {"pid": 12345, "tid": 9, "ts": 1755547993673433, "dur": 2148, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755547993675582, "dur": 349, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755547993675932, "dur": 355, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755547993676287, "dur": 382, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755547993676669, "dur": 370, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755547993677040, "dur": 370, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755547993677411, "dur": 354, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755547993677765, "dur": 379, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755547993678144, "dur": 368, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755547993678512, "dur": 366, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755547993678878, "dur": 399, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755547993679277, "dur": 379, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755547993679656, "dur": 416, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755547993680072, "dur": 479, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755547993680551, "dur": 480, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755547993681032, "dur": 534, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755547993681566, "dur": 551, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755547993682117, "dur": 521, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755547993682638, "dur": 493, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755547993683132, "dur": 504, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755547993683636, "dur": 531, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755547993684167, "dur": 491, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755547993684658, "dur": 472, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755547993685131, "dur": 492, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755547993685633, "dur": 464, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755547993686097, "dur": 498, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755547993686595, "dur": 543, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755547993687176, "dur": 831, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755547993688007, "dur": 465, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755547993688473, "dur": 411, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Profiling.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1755547993688885, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755547993688964, "dur": 795, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Profiling.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1755547993689760, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755547993689919, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.UI.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1755547993690158, "dur": 1129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.UI.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1755547993691288, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755547993691456, "dur": 322, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1755547993691779, "dur": 713, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755547993692495, "dur": 949, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1755547993693445, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755547993693522, "dur": 861, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1755547993694384, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755547993694493, "dur": 739, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.PlayMode.Scenarios.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1755547993695233, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755547993695317, "dur": 1116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.PlayMode.Scenarios.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1755547993696433, "dur": 208, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755547993696703, "dur": 832, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1755547993697535, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755547993697670, "dur": 144, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755547993697815, "dur": 781, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1755547993698597, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755547993698713, "dur": 505, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755547993699219, "dur": 401, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755547993699622, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1755547993699817, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755547993699889, "dur": 1356, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1755547993701308, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1755547993701407, "dur": 385, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1755547993701863, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/com.unity.cinemachine.editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1755547993702068, "dur": 463, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/com.unity.cinemachine.editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1755547993702531, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755547993702593, "dur": 66343, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755547993768945, "dur": 2915, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1755547993771861, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755547993772015, "dur": 2475, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1755547993774491, "dur": 4183, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755547993778689, "dur": 1975, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1755547993780672, "dur": 3721, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755547993784397, "dur": 2320, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1755547993786717, "dur": 221, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755547993786944, "dur": 3760, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mobile.AndroidLogcat.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1755547993790705, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755547993790857, "dur": 3701, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AdaptivePerformance.Profiler.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1755547993794558, "dur": 920, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755547993795608, "dur": 654, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755547993796312, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/EncryptionTransportEditor.pdb"}}, {"pid": 12345, "tid": 9, "ts": 1755547993796387, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755547993796668, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755547993796815, "dur": 264, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755547993797164, "dur": 667053, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755547994464265, "dur": 464619, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 9, "ts": 1755547994464219, "dur": 464667, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 9, "ts": 1755547994928919, "dur": 1837, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 10, "ts": 1755547993639915, "dur": 26021, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755547993666350, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_BC191A0A0D36605B.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1755547993667559, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1755547993668488, "dur": 390, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Profiler.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1755547993669179, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1755547993669936, "dur": 360, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755547993670344, "dur": 1949, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.cinemachine@dcc61ebd6655\\Runtime\\Behaviours\\CinemachineTargetGroup.cs"}}, {"pid": 12345, "tid": 10, "ts": 1755547993670297, "dur": 2267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755547993673179, "dur": 532, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.47f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\Microsoft.Extensions.Configuration.dll"}}, {"pid": 12345, "tid": 10, "ts": 1755547993672564, "dur": 1219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755547993673784, "dur": 654, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755547993674832, "dur": 1248, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Editor\\inspectors\\AnimationTrackInspector.cs"}}, {"pid": 12345, "tid": 10, "ts": 1755547993674438, "dur": 1796, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755547993676234, "dur": 467, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755547993676701, "dur": 456, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755547993677157, "dur": 450, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755547993677607, "dur": 482, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755547993678090, "dur": 450, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755547993678540, "dur": 501, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755547993679041, "dur": 512, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755547993679553, "dur": 522, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755547993680075, "dur": 517, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755547993680593, "dur": 495, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755547993681089, "dur": 501, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755547993681590, "dur": 477, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755547993682067, "dur": 525, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755547993682593, "dur": 481, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755547993683074, "dur": 520, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755547993683595, "dur": 479, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755547993684074, "dur": 518, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755547993684593, "dur": 480, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755547993685073, "dur": 464, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755547993685538, "dur": 473, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755547993686060, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755547993686300, "dur": 889, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755547993687189, "dur": 808, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755547993687997, "dur": 476, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755547993688474, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Playmode.Common.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1755547993688676, "dur": 1679, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755547993690359, "dur": 1064, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Playmode.Common.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1755547993691423, "dur": 188, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755547993691648, "dur": 228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Playmode.VirtualProjects.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1755547993691877, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755547993691993, "dur": 747, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Playmode.VirtualProjects.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1755547993692740, "dur": 337, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755547993693110, "dur": 212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Playmode.Workflow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1755547993693323, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755547993693405, "dur": 899, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Playmode.Workflow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1755547993694304, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755547993694390, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Playmode.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1755547993694509, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755547993694606, "dur": 1010, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Playmode.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1755547993695617, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755547993695725, "dur": 356, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755547993696101, "dur": 1424, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Mirror.Examples.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1755547993697526, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755547993697652, "dur": 171, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755547993697823, "dur": 1471, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755547993699294, "dur": 2677, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755547993701971, "dur": 67007, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755547993768979, "dur": 4620, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1755547993773600, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755547993773683, "dur": 3436, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1755547993777119, "dur": 200, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755547993777328, "dur": 2325, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1755547993779653, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755547993779713, "dur": 4674, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1755547993784388, "dur": 558, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755547993784953, "dur": 2838, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1755547993787792, "dur": 1170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755547993788970, "dur": 1932, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AdaptivePerformance.UI.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1755547993790958, "dur": 3241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1755547993794200, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755547993794324, "dur": 1008, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755547993795347, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755547993795425, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755547993795721, "dur": 205, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755547993795988, "dur": 208, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755547993796228, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755547993796293, "dur": 229, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755547993796527, "dur": 1063, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755547993797593, "dur": 1133455, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755547993639971, "dur": 25981, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755547993665960, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_276E0C7C26B49F57.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1755547993666329, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755547993666604, "dur": 1006, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_DD00D659C12B1C85.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1755547993668001, "dur": 481, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755547993668483, "dur": 335, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 11, "ts": 1755547993668819, "dur": 524, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755547993669378, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755547993669471, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755547993669957, "dur": 695, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755547993670652, "dur": 688, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755547993671341, "dur": 800, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755547993672141, "dur": 794, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755547993673172, "dur": 860, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.47f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\api-ms-win-crt-math-l1-1-0.dll"}}, {"pid": 12345, "tid": 11, "ts": 1755547993672936, "dur": 1491, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755547993674427, "dur": 1407, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755547993675834, "dur": 689, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755547993676524, "dur": 644, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755547993677169, "dur": 721, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755547993677890, "dur": 470, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755547993678360, "dur": 510, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755547993678871, "dur": 491, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755547993679362, "dur": 477, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755547993679839, "dur": 480, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755547993680319, "dur": 527, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755547993680846, "dur": 531, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755547993681377, "dur": 475, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755547993681852, "dur": 1063, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755547993682915, "dur": 688, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755547993683604, "dur": 673, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755547993684277, "dur": 561, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755547993684838, "dur": 502, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755547993685340, "dur": 540, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755547993685880, "dur": 670, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755547993686550, "dur": 635, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755547993687185, "dur": 844, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755547993688029, "dur": 510, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755547993688540, "dur": 313, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1755547993688854, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755547993688969, "dur": 801, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1755547993689771, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755547993689926, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PackageValidationSuite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1755547993690169, "dur": 1297, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PackageValidationSuite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1755547993691467, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755547993691633, "dur": 348, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PackageValidationSuite.Editor.Extension.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1755547993692026, "dur": 347, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1755547993692373, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755547993692476, "dur": 365, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1755547993692842, "dur": 247, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755547993693099, "dur": 930, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1755547993694030, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755547993694134, "dur": 1556, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mobile.AndroidLogcat.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1755547993695691, "dur": 732, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755547993696444, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Playmode.WorkflowUI.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1755547993696586, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1755547993696724, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755547993696830, "dur": 756, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1755547993697586, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755547993697666, "dur": 182, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755547993697848, "dur": 1399, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755547993699247, "dur": 2769, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755547993702017, "dur": 66921, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755547993768939, "dur": 2924, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/kcp2k.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1755547993771864, "dur": 1671, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755547993773539, "dur": 2597, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Telepathy.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1755547993776137, "dur": 6436, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755547993782581, "dur": 2521, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1755547993785103, "dur": 2501, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755547993787614, "dur": 2482, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1755547993790096, "dur": 1652, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755547993791753, "dur": 3440, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AdaptivePerformance.Simulator.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1755547993795194, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755547993795383, "dur": 192, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755547993795624, "dur": 493, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755547993796136, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755547993796327, "dur": 105, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/PPv2URPConverters.pdb"}}, {"pid": 12345, "tid": 11, "ts": 1755547993796435, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755547993796798, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755547993797000, "dur": 172, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Notifications.Android.pdb"}}, {"pid": 12345, "tid": 11, "ts": 1755547993797176, "dur": 1133920, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755547993640003, "dur": 26366, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755547993666374, "dur": 211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_7C8AF205DD25174E.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1755547993667677, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mirror.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1755547993668448, "dur": 334, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1755547993668783, "dur": 420, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755547993669203, "dur": 636, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Mirror.Editor.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1755547993669948, "dur": 384, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755547993670845, "dur": 742, "ph": "X", "name": "File", "args": {"detail": "Assets\\Mirror\\Examples\\_Common\\Controllers\\TankController\\TankTurretBase.cs"}}, {"pid": 12345, "tid": 12, "ts": 1755547993670333, "dur": 1277, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755547993671892, "dur": 766, "ph": "X", "name": "File", "args": {"detail": "Assets\\Mirror\\Hosting\\Edgegap\\Models\\SDK\\SessionGet.cs"}}, {"pid": 12345, "tid": 12, "ts": 1755547993671611, "dur": 1114, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755547993672725, "dur": 697, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755547993673422, "dur": 365, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755547993673787, "dur": 370, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755547993674157, "dur": 408, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755547993674828, "dur": 702, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@39bda2df468a\\Editor\\ShaderScriptableStripper.cs"}}, {"pid": 12345, "tid": 12, "ts": 1755547993674566, "dur": 1062, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755547993675628, "dur": 352, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755547993675980, "dur": 347, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755547993676327, "dur": 380, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755547993676707, "dur": 370, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755547993677077, "dur": 379, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755547993677456, "dur": 371, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755547993677827, "dur": 375, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755547993678203, "dur": 401, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755547993678604, "dur": 522, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755547993679126, "dur": 485, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755547993679611, "dur": 540, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755547993680152, "dur": 549, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755547993680701, "dur": 538, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755547993681239, "dur": 488, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755547993681728, "dur": 517, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755547993682267, "dur": 509, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Runtime\\VisualScripting.Core\\Reflection\\Operators\\OperatorUtility.cs"}}, {"pid": 12345, "tid": 12, "ts": 1755547993682245, "dur": 1263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755547993683509, "dur": 692, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755547993684201, "dur": 490, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755547993684692, "dur": 466, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755547993685158, "dur": 668, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755547993685826, "dur": 497, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755547993686323, "dur": 518, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755547993686841, "dur": 569, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755547993687410, "dur": 607, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755547993688018, "dur": 448, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755547993688467, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Mirror.CompilerSymbols.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1755547993688724, "dur": 2210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Mirror.CompilerSymbols.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1755547993690935, "dur": 447, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755547993691392, "dur": 772, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Mirror.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1755547993692164, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755547993692239, "dur": 780, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mirror.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1755547993693019, "dur": 1250, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755547993694282, "dur": 1990, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PackageValidationSuite.Editor.Extension.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1755547993696272, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755547993696443, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1755547993696655, "dur": 313, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEditorBridge.022.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1755547993697010, "dur": 719, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEditorBridge.022.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1755547993697787, "dur": 276, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1755547993698083, "dur": 448, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1755547993698531, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755547993698640, "dur": 567, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755547993699209, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Profiler.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1755547993699387, "dur": 326, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Profiler.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1755547993699714, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755547993699866, "dur": 2106, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755547993701973, "dur": 68181, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755547993770156, "dur": 3484, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Mirror.Examples.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1755547993773641, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755547993773786, "dur": 2687, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Profiling.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1755547993776524, "dur": 2377, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1755547993778901, "dur": 2426, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755547993781335, "dur": 5155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1755547993786491, "dur": 1057, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755547993787552, "dur": 4383, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1755547993791935, "dur": 2371, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755547993794314, "dur": 3127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Mirror.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1755547993797503, "dur": 1133458, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755547993640048, "dur": 26329, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755547993666381, "dur": 528, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_BA880D3F3B911009.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1755547993667066, "dur": 620, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755547993667809, "dur": 674, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755547993668483, "dur": 340, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 13, "ts": 1755547993668823, "dur": 420, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755547993669631, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755547993669944, "dur": 780, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755547993670844, "dur": 756, "ph": "X", "name": "File", "args": {"detail": "Assets\\Mirror\\Examples\\CCU\\Player.cs"}}, {"pid": 12345, "tid": 13, "ts": 1755547993670725, "dur": 1355, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755547993672080, "dur": 827, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755547993672907, "dur": 553, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755547993673461, "dur": 388, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755547993673850, "dur": 375, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755547993674226, "dur": 389, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755547993674833, "dur": 619, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@39bda2df468a\\Editor\\ShaderGraph\\AssetCallbacks\\CreateFullscreenShaderGraph.cs"}}, {"pid": 12345, "tid": 13, "ts": 1755547993674616, "dur": 1113, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755547993675729, "dur": 427, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755547993676156, "dur": 515, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755547993676671, "dur": 456, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755547993677127, "dur": 533, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755547993677660, "dur": 504, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755547993678164, "dur": 481, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755547993678645, "dur": 529, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755547993679174, "dur": 513, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755547993679688, "dur": 524, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755547993680212, "dur": 491, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755547993680703, "dur": 523, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755547993681226, "dur": 511, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755547993681737, "dur": 503, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755547993682240, "dur": 546, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755547993682786, "dur": 506, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755547993683292, "dur": 491, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755547993683783, "dur": 515, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755547993684298, "dur": 529, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755547993684828, "dur": 548, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755547993685376, "dur": 526, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755547993685902, "dur": 502, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755547993686404, "dur": 504, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755547993686909, "dur": 118, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755547993687027, "dur": 86, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755547993687113, "dur": 53, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755547993687166, "dur": 841, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755547993688008, "dur": 462, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755547993688472, "dur": 198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1755547993688671, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755547993688730, "dur": 1361, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1755547993690092, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755547993690257, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755547993690342, "dur": 238, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1755547993690580, "dur": 849, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755547993691432, "dur": 1734, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1755547993693166, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755547993693299, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755547993693355, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1755547993693511, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755547993693588, "dur": 2098, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1755547993695687, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755547993695758, "dur": 539, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755547993696304, "dur": 255, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1755547993696560, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755547993696649, "dur": 1091, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1755547993697740, "dur": 1411, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755547993699185, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1755547993699301, "dur": 518, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1755547993699819, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755547993699914, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1755547993700023, "dur": 355, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1755547993700378, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755547993700506, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Cinemachine.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1755547993700620, "dur": 384, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Cinemachine.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1755547993701004, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755547993701061, "dur": 957, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755547993702018, "dur": 66906, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755547993768926, "dur": 2805, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1755547993771732, "dur": 1770, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755547993773511, "dur": 3265, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1755547993776777, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755547993776876, "dur": 2950, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Mirror.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1755547993779827, "dur": 1170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755547993781003, "dur": 2839, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.PlayMode.Configurations.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1755547993783843, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755547993783979, "dur": 6079, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PackageValidationSuite.Editor.Extension.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1755547993790063, "dur": 1053, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755547993791126, "dur": 3867, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/com.unity.cinemachine.editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1755547993794994, "dur": 1219, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755547993796302, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.PlasticSCM.Editor.dll"}}, {"pid": 12345, "tid": 13, "ts": 1755547993796690, "dur": 501, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Flow.Editor.pdb"}}, {"pid": 12345, "tid": 13, "ts": 1755547993797192, "dur": 1133770, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755547993640138, "dur": 26262, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755547993666404, "dur": 892, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_9DC33C2F513A3B3C.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1755547993667676, "dur": 215, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1755547993667910, "dur": 17596, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1755547993685508, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755547993685729, "dur": 774, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@c854d1f7d97f\\Editor\\Developer\\ShelvedChangesNotification.cs"}}, {"pid": 12345, "tid": 14, "ts": 1755547993685693, "dur": 1233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755547993687009, "dur": 56, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755547993687090, "dur": 72, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755547993687202, "dur": 793, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755547993687998, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755547993688054, "dur": 732, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755547993688787, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.iOS.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1755547993689020, "dur": 821, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.iOS.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1755547993689842, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755547993689955, "dur": 199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/SimpleWebTransport.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1755547993690154, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755547993690238, "dur": 1137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/SimpleWebTransport.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1755547993691376, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755547993691494, "dur": 236, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1755547993691730, "dur": 1930, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755547993693670, "dur": 909, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1755547993694622, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Mirror.Authenticators.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1755547993694751, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Edgegap.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1755547993694873, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1755547993695019, "dur": 870, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Edgegap.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1755547993695889, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755547993696022, "dur": 1292, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/EncryptionTransportEditor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1755547993697315, "dur": 253, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755547993697657, "dur": 158, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755547993697815, "dur": 1478, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755547993699293, "dur": 2685, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755547993701978, "dur": 66968, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755547993768948, "dur": 3683, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1755547993772632, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755547993772751, "dur": 2572, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1755547993775324, "dur": 526, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755547993775858, "dur": 2491, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1755547993778349, "dur": 1827, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755547993780184, "dur": 3300, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1755547993783485, "dur": 2508, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755547993786002, "dur": 4545, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AdaptivePerformance.Simulator.Extension.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1755547993790548, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755547993790665, "dur": 2849, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.PlayMode.Scenarios.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1755547993793514, "dur": 793, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755547993794316, "dur": 2890, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1755547993797284, "dur": 1133653, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755547993640111, "dur": 26281, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755547993666398, "dur": 705, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_C2658D8F62234B56.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1755547993667260, "dur": 433, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755547993668421, "dur": 160, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mobile.AndroidLogcat.Editor.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1755547993668642, "dur": 1078, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1755547993669844, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2772139499707579658.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1755547993669923, "dur": 354, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755547993670278, "dur": 476, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755547993670755, "dur": 416, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755547993671171, "dur": 434, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755547993671606, "dur": 313, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755547993671919, "dur": 303, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755547993672223, "dur": 588, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755547993672811, "dur": 479, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755547993673290, "dur": 451, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755547993673741, "dur": 483, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755547993674225, "dur": 477, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755547993674702, "dur": 1326, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755547993676029, "dur": 1094, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755547993677123, "dur": 665, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755547993677789, "dur": 658, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755547993678448, "dur": 679, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755547993679128, "dur": 661, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755547993679789, "dur": 686, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755547993680475, "dur": 679, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755547993681154, "dur": 667, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755547993681821, "dur": 699, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755547993682520, "dur": 688, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755547993683208, "dur": 664, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755547993683872, "dur": 670, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755547993684542, "dur": 684, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755547993685226, "dur": 700, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755547993685927, "dur": 769, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755547993686696, "dur": 687, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755547993687384, "dur": 612, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755547993687996, "dur": 789, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755547993688786, "dur": 364, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.Android.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1755547993689187, "dur": 588, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.Android.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1755547993689776, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755547993689937, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1755547993690090, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755547993690161, "dur": 490, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1755547993690652, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755547993690812, "dur": 379, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755547993691195, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1755547993691393, "dur": 239, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1755547993691632, "dur": 838, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755547993692479, "dur": 743, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1755547993693223, "dur": 476, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755547993693705, "dur": 485, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1755547993694242, "dur": 1057, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1755547993695300, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755547993695403, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1755547993695534, "dur": 2138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1755547993697673, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755547993697786, "dur": 349, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1755547993698136, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755547993698215, "dur": 414, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1755547993698630, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755547993698730, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1755547993698856, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755547993698916, "dur": 501, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1755547993699418, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755547993699586, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1755547993699808, "dur": 553, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1755547993700362, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755547993700494, "dur": 1376, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755547993701875, "dur": 249, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1755547993702148, "dur": 465, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1755547993702652, "dur": 251, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1755547993702946, "dur": 250, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1755547993704104, "dur": 166112, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1755547993874449, "dur": 443898, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 15, "ts": 1755547993874435, "dur": 445235, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1755547994321043, "dur": 240, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755547994321823, "dur": 128715, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1755547994464127, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 15, "ts": 1755547994464113, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 15, "ts": 1755547994464236, "dur": 2145, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 15, "ts": 1755547994466385, "dur": 464526, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755547993640183, "dur": 26225, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755547993666409, "dur": 973, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_B2C9ADF926E837D8.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1755547993667575, "dur": 685, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.Android.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 16, "ts": 1755547993668448, "dur": 323, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1755547993668773, "dur": 211, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755547993669206, "dur": 434, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1755547993669711, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/11101650296562027598.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1755547993669940, "dur": 615, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755547993670555, "dur": 592, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755547993671147, "dur": 564, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755547993671711, "dur": 589, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755547993672300, "dur": 841, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755547993673141, "dur": 358, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755547993673500, "dur": 455, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755547993673955, "dur": 487, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755547993674829, "dur": 656, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Editor\\Animation\\CurvesProxy.cs"}}, {"pid": 12345, "tid": 16, "ts": 1755547993675486, "dur": 641, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Editor\\Animation\\CurveDataSource.cs"}}, {"pid": 12345, "tid": 16, "ts": 1755547993674442, "dur": 1753, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755547993676195, "dur": 659, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755547993676854, "dur": 668, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755547993677523, "dur": 654, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755547993678177, "dur": 645, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755547993678823, "dur": 657, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755547993679481, "dur": 692, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755547993680174, "dur": 640, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755547993680814, "dur": 679, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755547993681493, "dur": 728, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755547993682698, "dur": 660, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Runtime\\VisualScripting.Core\\Reflection\\Optimization\\IOptimizedAccessor.cs"}}, {"pid": 12345, "tid": 16, "ts": 1755547993682221, "dur": 1375, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755547993683597, "dur": 655, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755547993684252, "dur": 698, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755547993684950, "dur": 677, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755547993685633, "dur": 717, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755547993686350, "dur": 1067, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755547993687417, "dur": 605, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755547993688022, "dur": 442, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755547993688466, "dur": 402, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1755547993688869, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755547993688956, "dur": 956, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1755547993689913, "dur": 310, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755547993690241, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755547993690317, "dur": 388, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1755547993690743, "dur": 717, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1755547993691461, "dur": 436, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755547993691906, "dur": 249, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.PlayMode.Configurations.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1755547993692192, "dur": 778, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.PlayMode.Configurations.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1755547993692971, "dur": 345, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755547993693367, "dur": 1183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1755547993694551, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755547993694679, "dur": 4105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1755547993698785, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755547993698933, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1755547993699109, "dur": 843, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1755547993699953, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755547993700095, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1755547993700315, "dur": 619, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1755547993700934, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755547993701005, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1755547993701107, "dur": 255, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1755547993701420, "dur": 571, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755547993701991, "dur": 66936, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755547993768930, "dur": 3509, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Cinemachine.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1755547993772441, "dur": 781, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755547993773234, "dur": 3554, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/SimpleWebTransport.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1755547993776789, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755547993776886, "dur": 3541, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Mirror.Authenticators.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1755547993780428, "dur": 587, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755547993781024, "dur": 3692, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Updater.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1755547993784717, "dur": 403, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755547993785131, "dur": 4177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Playmode.Workflow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1755547993789315, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755547993789410, "dur": 3714, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1755547993793125, "dur": 271, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755547993793405, "dur": 3393, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1755547993796799, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755547993796942, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755547993797153, "dur": 170, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755547993797341, "dur": 1133754, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1755547994939916, "dur": 2894, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 13804, "tid": 164368, "ts": 1755547994959195, "dur": 2697, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 13804, "tid": 164368, "ts": 1755547994961968, "dur": 1944, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 13804, "tid": 164368, "ts": 1755547994955149, "dur": 9425, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}